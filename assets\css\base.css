/* AAcodenecFlex CSS Framework v1.0 */

/* CSS Custom Properties for Theme */
:root {
  --aac-primary: #2563eb;
  --aac-secondary: #64748b;
  --aac-success: #10b981;
  --aac-warning: #f59e0b;
  --aac-danger: #ef4444;
  --aac-light: #f8fafc;
  --aac-dark: #0f172a;
  --aac-white: #ffffff;
  --aac-gray-50: #f8fafc;
  --aac-gray-100: #f1f5f9;
  --aac-gray-200: #e2e8f0;
  --aac-gray-300: #cbd5e1;
  --aac-gray-400: #94a3b8;
  --aac-gray-500: #64748b;
  --aac-gray-600: #475569;
  --aac-gray-700: #334155;
  --aac-gray-800: #1e293b;
  --aac-gray-900: #0f172a;

  /* Background Colors */
  --aac-bg-primary: var(--aac-white);
  --aac-bg-secondary: var(--aac-gray-50);
  --aac-text-primary: var(--aac-gray-900);
  --aac-text-secondary: var(--aac-gray-600);
  --aac-border-color: var(--aac-gray-200);

  /* Pastel Color Variables */
  --aac-pastel-pink: #ffc0cb;
  --aac-pastel-lavender: #e6e6fa;
  --aac-pastel-blue: #b6d7ff;
  --aac-pastel-mint: #b8f2b8;
  --aac-pastel-peach: #ffcba4;
  --aac-pastel-yellow: #fff5b7;
  --aac-pastel-coral: #ffb3ba;
  --aac-pastel-sage: #c8d5b9;
  --aac-pastel-lilac: #dda0dd;
  --aac-pastel-sky: #87ceeb;
  --aac-pastel-cream: #f5f5dc;
  --aac-pastel-rose: #ffc0cb;
  --aac-pastel-aqua: #b0e0e6;
  --aac-pastel-lemon: #fffacd;
  --aac-pastel-mauve: #e0b4d6;
  --aac-pastel-seafoam: #9fe2bf;
}

/* Dark Mode Theme */
[data-theme="dark"] {
  --aac-bg-primary: var(--aac-gray-900);
  --aac-bg-secondary: var(--aac-gray-800);
  --aac-text-primary: var(--aac-gray-50);
  --aac-text-secondary: var(--aac-gray-300);
  --aac-border-color: var(--aac-gray-700);
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    line-height: 1.6;
    color: var(--aac-text-primary);
    background-color: var(--aac-bg-primary);
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Reset and Base Styles */
*,
*::before,
*::after {
  box-sizing: border-box;
}

* {
  margin: 0;
  padding: 0;
}

ul {
  margin: 0;
  padding: 0;
}

ul li {
  position: relative;
  margin: 8px 0;
}

ul li ul {
  margin-left: 20px;
}

input {
    padding: 0.5rem 1rem;
    border: 1px solid var(--aac-border-color);
    background-color: var(--aac-bg-secondary);
    color: var(--aac-text-primary);
    font-size: 0.875rem;
    transition: all 0.3s ease;
}
input:focus {
  outline: none;
  border-color: var(--aac-primary);
}
input[type="submit"] {
    background: var(--aac-primary);
    color: var(--aac-white);
    border: none;
    cursor: pointer;
    font-size: 1rem;
    transition: background 0.3s ease;
}
img{
  max-width: 100%;
  vertical-align: middle;
}

/* Typography */
.aac-h1 {
  font-size: 2.25rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
}
.aac-h2 {
  font-size: 1.875rem;
  font-weight: 600;
  line-height: 1.3;
  margin-bottom: 0.875rem;
}
.aac-h3 {
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 1.4;
  margin-bottom: 0.75rem;
}
.aac-h4 {
  font-size: 1.25rem;
  font-weight: 500;
  line-height: 1.4;
  margin-bottom: 0.625rem;
}
.aac-h5 {
  font-size: 1.125rem;
  font-weight: 500;
  line-height: 1.5;
  margin-bottom: 0.5rem;
}
.aac-h6 {
  font-size: 1rem;
  font-weight: 500;
  line-height: 1.5;
  margin-bottom: 0.5rem;
}

.aac-text-xs {
  font-size: 0.75rem;
}
.aac-text-sm {
  font-size: 0.875rem;
}
.aac-text-base {
  font-size: 1rem;
}
.aac-text-lg {
  font-size: 1.125rem;
}
.aac-text-xl {
  font-size: 1.25rem;
}
.aac-text-2xl {
  font-size: 1.5rem;
}

.aac-text-primary {
  color: var(--aac-text-primary);
}
.aac-text-secondary {
  color: var(--aac-text-secondary);
}
.aac-text-white {
  color: var(--aac-white);
}

/* Container System */
.aac-container {
  width: 100%;
  margin: 0 auto;
  padding: 0 1rem;
}

.aac-container-sm {
  max-width: 640px;
}
.aac-container-md {
  max-width: 768px;
}
.aac-container-lg {
  max-width: 1024px;
}
.aac-container-xl {
  max-width: 1280px;
}
.aac-container-2xl {
  max-width: 1536px;
}

/* Flexbox Grid System */
.aac-row {
  --aac-gutter-x:1.5rem;
  --aac-gutter-y: 0;
  display: flex;
  flex-wrap: wrap;
  margin-top: calc(-1 * var(--aac-gutter-y));
  margin-right: calc(-0.5 * var(--aac-gutter-x));
  margin-left: calc(-0.5 * var(--aac-gutter-x));
}   

.aac-row > *{
    flex-shrink: 0;
    width: 100%;
    max-width: 100%;
    padding-right: calc(var(--aac-gutter-x) * 0.5);
    padding-left: calc(var(--aac-gutter-x) * 0.5);
    margin-top: var(--aac-gutter-y);
}

.aac-col {
  flex: 1;
  padding: 0 0.75rem;
}

/* Column Sizes */
.aac-col-1 {
  flex: 0 0 8.333333%;
  max-width: 8.333333%;
}
.aac-col-2 {
  flex: 0 0 16.666667%;
  max-width: 16.666667%;
}
.aac-col-3 {
  flex: 0 0 25%;
  max-width: 25%;
}
.aac-col-4 {
  flex: 0 0 33.333333%;
  max-width: 33.333333%;
}
.aac-col-5 {
  flex: 0 0 41.666667%;
  max-width: 41.666667%;
}
.aac-col-6 {
  flex: 0 0 50%;
  max-width: 50%;
}
.aac-col-7 {
  flex: 0 0 58.333333%;
  max-width: 58.333333%;
}
.aac-col-8 {
  flex: 0 0 66.666667%;
  max-width: 66.666667%;
}
.aac-col-9 {
  flex: 0 0 75%;
  max-width: 75%;
}
.aac-col-10 {
  flex: 0 0 83.333333%;
  max-width: 83.333333%;
}
.aac-col-11 {
  flex: 0 0 91.666667%;
  max-width: 91.666667%;
}
.aac-col-12 {
  flex: 0 0 100%;
  max-width: 100%;
}

/* Flexbox Utilities */
.aac-d-flex {
  display: flex;
}
.aac-d-inline-flex {
  display: inline-flex;
}
.aac-flex-row {
  flex-direction: row;
}
.aac-flex-column {
  flex-direction: column;
}
.aac-flex-wrap {
  flex-wrap: wrap;
}
.aac-flex-nowrap {
  flex-wrap: nowrap;
}

.aac-justify-start {
  justify-content: flex-start;
}
.aac-justify-center {
  justify-content: center;
}
.aac-justify-end {
  justify-content: flex-end;
}
.aac-justify-between {
  justify-content: space-between;
}
.aac-justify-around {
  justify-content: space-around;
}
.aac-justify-evenly {
  justify-content: space-evenly;
}

.aac-align-start {
  align-items: flex-start;
}
.aac-align-center {
  align-items: center;
}
.aac-align-end {
  align-items: flex-end;
}
.aac-align-stretch {
  align-items: stretch;
}
.aac-align-baseline {
  align-items: baseline;
}

/* Spacing Utilities */
.aac-m-0 {
  margin: 0;
}
.aac-m-1 {
  margin: 0.25rem;
}
.aac-m-2 {
  margin: 0.5rem;
}
.aac-m-3 {
  margin: 0.75rem;
}
.aac-m-4 {
  margin: 1rem;
}
.aac-m-5 {
  margin: 1.25rem;
}
.aac-m-6 {
  margin: 1.5rem;
}
.aac-m-8 {
  margin: 2rem;
}

.aac-p-0 {
  padding: 0;
}
.aac-p-1 {
  padding: 0.25rem;
}
.aac-p-2 {
  padding: 0.5rem;
}
.aac-p-3 {
  padding: 0.75rem;
}
.aac-p-4 {
  padding: 1rem;
}
.aac-p-5 {
  padding: 1.25rem;
}
.aac-p-6 {
  padding: 1.5rem;
}
.aac-p-8 {
  padding: 2rem;
}

.aac-mt-0 {
  margin-top: 0;
}
.aac-mt-1 {
  margin-top: 0.25rem;
}
.aac-mt-2 {
  margin-top: 0.5rem;
}
.aac-mt-3 {
  margin-top: 0.75rem;
}
.aac-mt-4 {
  margin-top: 1rem;
}
.aac-mt-5 {
  margin-top: 1.25rem;
}
.aac-mt-6 {
  margin-top: 1.5rem;
}
.aac-mt-8 {
  margin-top: 2rem;
}

.aac-mb-0 {
  margin-bottom: 0;
}
.aac-mb-1 {
  margin-bottom: 0.25rem;
}
.aac-mb-2 {
  margin-bottom: 0.5rem;
}
.aac-mb-3 {
  margin-bottom: 0.75rem;
}
.aac-mb-4 {
  margin-bottom: 1rem;
}
.aac-mb-5 {
  margin-bottom: 1.25rem;
}
.aac-mb-6 {
  margin-bottom: 1.5rem;
}
.aac-mb-8 {
  margin-bottom: 2rem;
}

.aac-ml-0 {
  margin-left: 0;
}
.aac-ml-1 {
  margin-left: 0.25rem;
}
.aac-ml-2 {
  margin-left: 0.5rem;
}
.aac-ml-3 {
  margin-left: 0.75rem;
}
.aac-ml-4 {
  margin-left: 1rem;
}
.aac-ml-5 {
  margin-left: 1.25rem;
}
.aac-ml-6 {
  margin-left: 1.5rem;
}
.aac-ml-8 {
  margin-left: 2rem;
}

.aac-mr-0 {
  margin-right: 0;
}
.aac-mr-1 {
  margin-right: 0.25rem;
}
.aac-mr-2 {
  margin-right: 0.5rem;
}
.aac-mr-3 {
  margin-right: 0.75rem;
}
.aac-mr-4 {
  margin-right: 1rem;
}
.aac-mr-5 {
  margin-right: 1.25rem;
}
.aac-mr-6 {
  margin-right: 1.5rem;
}
.aac-mr-8 {
  margin-right: 2rem;
}

.aac-pt-0 {
  padding-top: 0;
}
.aac-pt-1 {
  padding-top: 0.25rem;
}
.aac-pt-2 {
  padding-top: 0.5rem;
}
.aac-pt-3 {
  padding-top: 0.75rem;
}
.aac-pt-4 {
  padding-top: 1rem;
}
.aac-pt-5 {
  padding-top: 1.25rem;
}
.aac-pt-6 {
  padding-top: 1.5rem;
}
.aac-pt-8 {
  padding-top: 2rem;
}

.aac-pb-0 {
  padding-bottom: 0;
}
.aac-pb-1 {
  padding-bottom: 0.25rem;
}
.aac-pb-2 {
  padding-bottom: 0.5rem;
}
.aac-pb-3 {
  padding-bottom: 0.75rem;
}
.aac-pb-4 {
  padding-bottom: 1rem;
}
.aac-pb-5 {
  padding-bottom: 1.25rem;
}
.aac-pb-6 {
  padding-bottom: 1.5rem;
}
.aac-pb-8 {
  padding-bottom: 2rem;
}

.aac-pl-0 {
  padding-left: 0;
}
.aac-pl-1 {
  padding-left: 0.25rem;
}
.aac-pl-2 {
  padding-left: 0.5rem;
}
.aac-pl-3 {
  padding-left: 0.75rem;
}
.aac-pl-4 {
  padding-left: 1rem;
}
.aac-pl-5 {
  padding-left: 1.25rem;
}
.aac-pl-6 {
  padding-left: 1.5rem;
}
.aac-pl-8 {
  padding-left: 2rem;
}

.aac-pr-0 {
  padding-right: 0;
}
.aac-pr-1 {
  padding-right: 0.25rem;
}
.aac-pr-2 {
  padding-right: 0.5rem;
}
.aac-pr-3 {
  padding-right: 0.75rem;
}
.aac-pr-4 {
  padding-right: 1rem;
}
.aac-pr-5 {
  padding-right: 1.25rem;
}
.aac-pr-6 {
  padding-right: 1.5rem;
}
.aac-pr-8 {
  padding-right: 2rem;
}

/* Button Components */
.aac-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  border: 1px solid transparent;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s ease;
  background: none;
  font-family: inherit;
}

.aac-btn-primary {
  background-color: var(--aac-primary);
  color: var(--aac-white);
  border-color: var(--aac-primary);
}

.aac-btn-primary:hover {
  background-color: #1d4ed8;
  border-color: #1d4ed8;
}

.aac-btn-secondary {
  background-color: var(--aac-secondary);
  color: var(--aac-white);
  border-color: var(--aac-secondary);
}

.aac-btn-secondary:hover {
  background-color: #475569;
  border-color: #475569;
}

.aac-btn-outline-primary {
  color: var(--aac-primary);
  border-color: var(--aac-primary);
  background-color: transparent;
}

.aac-btn-outline-primary:hover {
  background-color: var(--aac-primary);
  color: var(--aac-white);
}

.aac-btn-danger {
  background-color: var(--aac-danger);
  color: var(--aac-white);
  border-color: var(--aac-danger);
}

.aac-btn-danger:hover {
  background-color: #dc2626;
  border-color: #dc2626;
}

.aac-btn-outline-danger {
  color: var(--aac-danger);
  border-color: var(--aac-danger);
  background-color: transparent;
}

.aac-btn-outline-danger:hover {
  background-color: var(--aac-danger);
  color: var(--aac-white);
}

.aac-btn-sm {
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
}

.aac-btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
}

/* Card Component */
.aac-card {
  background-color: var(--aac-bg-primary);
  border: 1px solid var(--aac-border-color);
  border-radius: 0.5rem;
  overflow: hidden;
  transition: border-color 0.3s ease;
}

.aac-card-header {
  padding: 1rem;
  border-bottom: 1px solid var(--aac-border-color);
  background-color: var(--aac-bg-secondary);
}

.aac-card-body {
  padding: 1rem;
}

.aac-card-footer {
  padding: 1rem;
  border-top: 1px solid var(--aac-border-color);
  background-color: var(--aac-bg-secondary);
}

/* Alert Components */
.aac-alert {
  padding: 0.75rem 1rem;
  border-radius: 0.375rem;
  border: 1px solid transparent;
  margin-bottom: 1rem;
}

.aac-alert-primary {
  background-color: #dbeafe;
  border-color: #93c5fd;
  color: #1e40af;
}

[data-theme="dark"] .aac-alert-primary {
  background-color: rgba(37, 99, 235, 0.1);
  border-color: rgba(37, 99, 235, 0.3);
  color: #93c5fd;
}

.aac-alert-success {
  background-color: #d1fae5;
  border-color: #6ee7b7;
  color: #065f46;
}

[data-theme="dark"] .aac-alert-success {
  background-color: rgba(16, 185, 129, 0.1);
  border-color: rgba(16, 185, 129, 0.3);
  color: #6ee7b7;
}

.aac-alert-warning {
  background-color: #fef3c7;
  border-color: #fcd34d;
  color: #92400e;
}

[data-theme="dark"] .aac-alert-warning {
  background-color: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.3);
  color: #fcd34d;
}

.aac-alert-danger {
  background-color: #fee2e2;
  border-color: #fca5a5;
  color: #991b1b;
}

[data-theme="dark"] .aac-alert-danger {
  background-color: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
  color: #fca5a5;
}

/* Form Components */
.aac-form-group {
  margin-bottom: 1rem;
}

.aac-form-label {
  display: block;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: var(--aac-text-primary);
}

.aac-form-control {
  width: 100%;
  padding: 0.5rem 0.75rem;
  font-size: 1rem;
  border: 1px solid var(--aac-border-color);
  border-radius: 0.375rem;
  background-color: var(--aac-bg-primary);
  color: var(--aac-text-primary);
  transition: border-color 0.2s ease;
  font-family: inherit;
}

.aac-form-control:focus {
  outline: none;
  border-color: var(--aac-primary);
}

/* Utilities */
.aac-text-center {
  text-align: center;
}
.aac-text-left {
  text-align: left;
}
.aac-text-right {
  text-align: right;
}

.aac-w-100 {
  width: 100%;
}
.aac-h-100 {
  height: 100%;
}

.aac-rounded {
  border-radius: 0.25rem;
}
.aac-rounded-lg {
  border-radius: 0.5rem;
}
.aac-rounded-xl {
  border-radius: 0.75rem;
}

.aac-border {
  border: 1px solid var(--aac-border-color);
}
.aac-border-0 {
  border: none;
}

/* Background Colors */
.aac-bg-primary {
  background-color: var(--aac-bg-primary);
}
.aac-bg-secondary {
  background-color: var(--aac-bg-secondary);
}
.aac-bg-light {
  background-color: var(--aac-light);
}
.aac-bg-dark {
  background-color: var(--aac-dark);
}

/* Additional Flat Colors */
.aac-bg-success {
  background-color: var(--aac-success);
}
.aac-bg-warning {
  background-color: var(--aac-warning);
}
.aac-bg-danger {
  background-color: var(--aac-danger);
}
.aac-bg-light {
  background-color: var(--aac-light);
}
.aac-bg-dark {
  background-color: var(--aac-dark);
}
.aac-bg-white {
  background-color: var(--aac-white);
}

/* Gray Background Colors */
.aac-bg-gray-50 {
  background-color: var(--aac-gray-50);
}
.aac-bg-gray-100 {
  background-color: var(--aac-gray-100);
}
.aac-bg-gray-200 {
  background-color: var(--aac-gray-200);
}
.aac-bg-gray-300 {
  background-color: var(--aac-gray-300);
}
.aac-bg-gray-400 {
  background-color: var(--aac-gray-400);
}
.aac-bg-gray-500 {
  background-color: var(--aac-gray-500);
}
.aac-bg-gray-600 {
  background-color: var(--aac-gray-600);
}
.aac-bg-gray-700 {
  background-color: var(--aac-gray-700);
}
.aac-bg-gray-800 {
  background-color: var(--aac-gray-800);
}
.aac-bg-gray-900 {
  background-color: var(--aac-gray-900);
}

/* Gradient Backgrounds */
.aac-gradient-primary {
  background: linear-gradient(135deg, var(--aac-primary), #1d4ed8);
}

.aac-gradient-secondary {
  background: linear-gradient(135deg, var(--aac-secondary), #475569);
}

.aac-gradient-success {
  background: linear-gradient(135deg, var(--aac-success), #059669);
}

.aac-gradient-warning {
  background: linear-gradient(135deg, var(--aac-warning), #d97706);
}

.aac-gradient-danger {
  background: linear-gradient(135deg, var(--aac-danger), #dc2626);
}

.aac-gradient-blue {
  background: linear-gradient(135deg, #3b82f6, #1e40af);
}

.aac-gradient-purple {
  background: linear-gradient(135deg, #8b5cf6, #6d28d9);
}

.aac-gradient-pink {
  background: linear-gradient(135deg, #ec4899, #be185d);
}

.aac-gradient-orange {
  background: linear-gradient(135deg, #f97316, #ea580c);
}

.aac-gradient-teal {
  background: linear-gradient(135deg, #14b8a6, #0f766e);
}

.aac-gradient-indigo {
  background: linear-gradient(135deg, #6366f1, #4338ca);
}

.aac-gradient-emerald {
  background: linear-gradient(135deg, #10b981, #047857);
}

/* Multi-color Gradients */
.aac-gradient-rainbow {
  background: linear-gradient(
    135deg,
    #ff6b6b,
    #4ecdc4,
    #45b7d1,
    #96ceb4,
    #feca57
  );
}

.aac-gradient-sunset {
  background: linear-gradient(135deg, #ff9a9e, #fecfef, #fecfef);
}

.aac-gradient-ocean {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.aac-gradient-forest {
  background: linear-gradient(135deg, #134e5e, #71b280);
}

/* Text Colors */
.aac-text-primary-color {
  color: var(--aac-primary);
}
.aac-text-secondary-color {
  color: var(--aac-secondary);
}
.aac-text-success {
  color: var(--aac-success);
}
.aac-text-warning {
  color: var(--aac-warning);
}
.aac-text-danger {
  color: var(--aac-danger);
}

/* Pastel Background Colors */
.aac-bg-pastel-pink {
  background-color: var(--aac-pastel-pink);
}
.aac-bg-pastel-lavender {
  background-color: var(--aac-pastel-lavender);
}
.aac-bg-pastel-blue {
  background-color: var(--aac-pastel-blue);
}
.aac-bg-pastel-mint {
  background-color: var(--aac-pastel-mint);
}
.aac-bg-pastel-peach {
  background-color: var(--aac-pastel-peach);
}
.aac-bg-pastel-yellow {
  background-color: var(--aac-pastel-yellow);
}
.aac-bg-pastel-coral {
  background-color: var(--aac-pastel-coral);
}
.aac-bg-pastel-sage {
  background-color: var(--aac-pastel-sage);
}
.aac-bg-pastel-lilac {
  background-color: var(--aac-pastel-lilac);
}
.aac-bg-pastel-sky {
  background-color: var(--aac-pastel-sky);
}
.aac-bg-pastel-cream {
  background-color: var(--aac-pastel-cream);
}
.aac-bg-pastel-rose {
  background-color: var(--aac-pastel-rose);
}
.aac-bg-pastel-aqua {
  background-color: var(--aac-pastel-aqua);
}
.aac-bg-pastel-lemon {
  background-color: var(--aac-pastel-lemon);
}
.aac-bg-pastel-mauve {
  background-color: var(--aac-pastel-mauve);
}
.aac-bg-pastel-seafoam {
  background-color: var(--aac-pastel-seafoam);
}

/* Pastel Text Colors */
.aac-text-pastel-pink {
  color: var(--aac-pastel-pink);
}
.aac-text-pastel-lavender {
  color: var(--aac-pastel-lavender);
}
.aac-text-pastel-blue {
  color: var(--aac-pastel-blue);
}
.aac-text-pastel-mint {
  color: var(--aac-pastel-mint);
}
.aac-text-pastel-peach {
  color: var(--aac-pastel-peach);
}
.aac-text-pastel-yellow {
  color: var(--aac-pastel-yellow);
}
.aac-text-pastel-coral {
  color: var(--aac-pastel-coral);
}
.aac-text-pastel-sage {
  color: var(--aac-pastel-sage);
}
.aac-text-pastel-lilac {
  color: var(--aac-pastel-lilac);
}
.aac-text-pastel-sky {
  color: var(--aac-pastel-sky);
}

/* Pastel Gradients */
.aac-gradient-pastel-dream {
  background: linear-gradient(
    135deg,
    var(--aac-pastel-pink),
    var(--aac-pastel-lavender),
    var(--aac-pastel-blue)
  );
}

.aac-gradient-pastel-spring {
  background: linear-gradient(
    135deg,
    var(--aac-pastel-mint),
    var(--aac-pastel-yellow),
    var(--aac-pastel-peach)
  );
}

.aac-gradient-pastel-sunset {
  background: linear-gradient(
    135deg,
    var(--aac-pastel-coral),
    var(--aac-pastel-peach),
    var(--aac-pastel-yellow)
  );
}

.aac-gradient-pastel-ocean {
  background: linear-gradient(
    135deg,
    var(--aac-pastel-aqua),
    var(--aac-pastel-sky),
    var(--aac-pastel-blue)
  );
}

.aac-gradient-pastel-garden {
  background: linear-gradient(
    135deg,
    var(--aac-pastel-sage),
    var(--aac-pastel-mint),
    var(--aac-pastel-seafoam)
  );
}

.aac-gradient-pastel-romance {
  background: linear-gradient(
    135deg,
    var(--aac-pastel-rose),
    var(--aac-pastel-mauve),
    var(--aac-pastel-lilac)
  );
}

/* Pastel Border Colors */
.aac-border-pastel-pink {
  border-color: var(--aac-pastel-pink);
}
.aac-border-pastel-blue {
  border-color: var(--aac-pastel-blue);
}
.aac-border-pastel-mint {
  border-color: var(--aac-pastel-mint);
}
.aac-border-pastel-peach {
  border-color: var(--aac-pastel-peach);
}
.aac-border-pastel-lavender {
  border-color: var(--aac-pastel-lavender);
}

/* Modal Components */
.aac-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  transition: opacity 0.3s ease;
}

.aac-modal.aac-modal-active {
  display: flex;
}

.aac-modal-content {
  background-color: var(--aac-bg-primary);
  border-radius: 0.5rem;
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  animation: aac-modal-slide-in 0.3s ease-out;
}

@keyframes aac-modal-slide-in {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.aac-modal-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--aac-border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.aac-modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--aac-text-primary);
  margin: 0;
}

.aac-modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--aac-text-secondary);
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: color 0.2s ease, background-color 0.2s ease;
}

.aac-modal-close:hover {
  color: var(--aac-text-primary);
  background-color: var(--aac-gray-200);
}

[data-theme="dark"] .aac-modal-close:hover {
  background-color: var(--aac-gray-700);
}

.aac-modal-body {
  padding: 1.5rem;
}

.aac-modal-footer {
  padding: 1.5rem;
  border-top: 1px solid var(--aac-border-color);
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

/* Modal Sizes */
.aac-modal-sm .aac-modal-content {
  max-width: 400px;
}
.aac-modal-lg .aac-modal-content {
  max-width: 800px;
}
.aac-modal-xl .aac-modal-content {
  max-width: 1200px;
}

/* Slide Menu Components */
.aac-slide-menu {
  position: fixed;
  top: 0;
  left: -300px;
  width: 300px;
  height: 100vh;
  background-color: var(--aac-bg-primary);
  border-right: 1px solid var(--aac-border-color);
  transition: left 0.3s ease;
  z-index: 999;
  overflow-y: auto;
}

.aac-slide-menu.aac-slide-menu-active {
  left: 0;
}

.aac-slide-menu-right {
  left: auto;
  right: -300px;
  border-left: 1px solid var(--aac-border-color);
  border-right: none;
}

.aac-slide-menu-right.aac-slide-menu-active {
  right: 0;
}

.aac-slide-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: none;
  z-index: 998;
  transition: opacity 0.3s ease;
}

.aac-slide-menu-overlay.aac-slide-menu-overlay-active {
  display: block;
}

.aac-slide-menu-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--aac-border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--aac-bg-secondary);
}

.aac-slide-menu-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--aac-text-primary);
  margin: 0;
}

.aac-slide-menu-close {
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  color: var(--aac-text-secondary);
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: color 0.2s ease, background-color 0.2s ease;
}

.aac-slide-menu-close:hover {
  color: var(--aac-text-primary);
  background-color: var(--aac-gray-200);
}

[data-theme="dark"] .aac-slide-menu-close:hover {
  background-color: var(--aac-gray-700);
}

.aac-slide-menu-body {
  padding: 1rem;
}

.aac-slide-menu-item {
  display: block;
  padding: 0.75rem 1rem;
  color: var(--aac-text-primary);
  text-decoration: none;
  border-radius: 0.375rem;
  margin-bottom: 0.25rem;
  transition: background-color 0.2s ease;
}

.aac-slide-menu-item:hover {
  background-color: var(--aac-gray-100);
  text-decoration: none;
}

[data-theme="dark"] .aac-slide-menu-item:hover {
  background-color: var(--aac-gray-700);
}

.aac-slide-menu-item.aac-slide-menu-item-active {
  background-color: var(--aac-primary);
  color: var(--aac-white);
}

.aac-slide-menu-divider {
  height: 1px;
  background-color: var(--aac-border-color);
  margin: 0.5rem 0;
}

/* Slide Menu Sizes */
.aac-slide-menu-sm {
  width: 250px;
  left: -250px;
}
.aac-slide-menu-sm.aac-slide-menu-right {
  right: -250px;
}
.aac-slide-menu-lg {
  width: 350px;
  left: -350px;
}
.aac-slide-menu-lg.aac-slide-menu-right {
  right: -350px;
}

/* Responsive Media Components */
.aac-img-responsive {
  max-width: 100%;
  height: auto;
  display: block;
}

.aac-img-fluid {
  max-width: 100%;
  height: auto;
}

.aac-img-thumbnail {
  max-width: 100%;
  height: auto;
  padding: 0.25rem;
  background-color: var(--aac-bg-primary);
  border: 1px solid var(--aac-border-color);
  border-radius: 0.375rem;
}

.aac-img-rounded {
  border-radius: 0.375rem;
}

.aac-img-circle {
  border-radius: 50%;
}

/* Responsive Video Container */
.aac-video-responsive {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  overflow: hidden;
  border-radius: 0.375rem;
}

.aac-video-responsive iframe,
.aac-video-responsive video,
.aac-video-responsive embed,
.aac-video-responsive object {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
}

/* Video Aspect Ratios */
.aac-video-16-9 {
  padding-bottom: 56.25%;
} /* 16:9 */
.aac-video-4-3 {
  padding-bottom: 75%;
} /* 4:3 */
.aac-video-21-9 {
  padding-bottom: 42.85%;
} /* 21:9 */
.aac-video-1-1 {
  padding-bottom: 100%;
} /* 1:1 Square */

/* Figure Component */
.aac-figure {
  display: inline-block;
  margin: 0 0 1rem 0;
}

.aac-figure-img {
  max-width: 100%;
  height: auto;
  line-height: 1;
}

.aac-figure-caption {
  padding: 0.5rem;
  color: var(--aac-text-secondary);
  font-size: 0.875rem;
  text-align: center;
}

/* Image Gallery */
.aac-gallery {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.aac-gallery-item {
  position: relative;
  overflow: hidden;
  border-radius: 0.375rem;
  transition: transform 0.3s ease;
}

.aac-gallery-item:hover {
  transform: scale(1.02);
}

.aac-gallery-item img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  display: block;
}

.aac-gallery-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  color: var(--aac-white);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.aac-gallery-item:hover .aac-gallery-overlay {
  opacity: 1;
}

/* Hero Image/Video Section */
.aac-hero {
  position: relative;
  width: 100%;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border-radius: 0.5rem;
}

.aac-hero-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: -1;
}

.aac-hero-content {
  position: relative;
  z-index: 1;
  text-align: center;
  color: var(--aac-white);
  padding: 2rem;
}

.aac-hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  z-index: 0;
}

/* Lightbox for Images */
.aac-lightbox {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.aac-lightbox.aac-lightbox-active {
  display: flex;
}

.aac-lightbox-content {
  position: relative;
  max-width: 90%;
  max-height: 90%;
}

.aac-lightbox-img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.aac-lightbox-close {
  position: absolute;
  top: -40px;
  right: 0;
  background: none;
  border: none;
  color: var(--aac-white);
  font-size: 2rem;
  cursor: pointer;
  padding: 0.5rem;
}

.aac-lightbox-close:hover {
  opacity: 0.7;
}

/* Responsive Image Sizes */
.aac-img-xs {
  max-width: 50px;
}
.aac-img-sm {
  max-width: 100px;
}
.aac-img-md {
  max-width: 200px;
}
.aac-img-lg {
  max-width: 300px;
}
.aac-img-xl {
  max-width: 400px;
}

/* Avatar Component */
.aac-avatar {
  display: inline-block;
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--aac-border-color);
}

.aac-avatar-xs {
  width: 1.5rem;
  height: 1.5rem;
}
.aac-avatar-sm {
  width: 2rem;
  height: 2rem;
}
.aac-avatar-md {
  width: 3rem;
  height: 3rem;
}
.aac-avatar-lg {
  width: 4rem;
  height: 4rem;
}
.aac-avatar-xl {
  width: 5rem;
  height: 5rem;
}

/* Responsive Table Components */
.aac-table {
  width: 100%;
  margin-bottom: 1rem;
  color: var(--aac-text-primary);
  border-collapse: collapse;
}

.aac-table th,
.aac-table td {
  padding: 0.75rem;
  vertical-align: top;
  border-top: 1px solid var(--aac-border-color);
  text-align: left;
}

.aac-table thead th {
  vertical-align: bottom;
  border-bottom: 2px solid var(--aac-border-color);
  background-color: var(--aac-bg-secondary);
  font-weight: 600;
  color: var(--aac-text-primary);
}

.aac-table tbody tr:hover {
  background-color: var(--aac-bg-secondary);
  transition: background-color 0.15s ease-in-out;
}

/* Table Variants */
.aac-table-striped tbody tr:nth-of-type(odd) {
  background-color: var(--aac-bg-secondary);
}

.aac-table-bordered {
  border: 1px solid var(--aac-border-color);
}

.aac-table-bordered th,
.aac-table-bordered td {
  border: 1px solid var(--aac-border-color);
}

.aac-table-borderless th,
.aac-table-borderless td,
.aac-table-borderless thead th {
  border: 0;
}

/* Table Sizes */
.aac-table-sm th,
.aac-table-sm td {
  padding: 0.375rem;
}

.aac-table-lg th,
.aac-table-lg td {
  padding: 1rem;
}

/* Responsive Table Container */
.aac-table-responsive {
  display: block;
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.aac-table-responsive > .aac-table {
  margin-bottom: 0;
}

/* Responsive Breakpoints for Tables */
@media (max-width: 575.98px) {
  .aac-table-responsive-sm {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}

@media (max-width: 767.98px) {
  .aac-table-responsive-md {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}

@media (max-width: 991.98px) {
  .aac-table-responsive-lg {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}

@media (max-width: 1199.98px) {
  .aac-table-responsive-xl {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}

/* Mobile Stack Table */
.aac-table-stack {
  border: 0;
}

@media (max-width: 767.98px) {
  .aac-table-stack,
  .aac-table-stack thead,
  .aac-table-stack tbody,
  .aac-table-stack th,
  .aac-table-stack td,
  .aac-table-stack tr {
    display: block;
  }

  .aac-table-stack thead tr {
    position: absolute;
    top: -9999px;
    left: -9999px;
  }

  .aac-table-stack tr {
    border: 1px solid var(--aac-border-color);
    border-radius: 0.375rem;
    margin-bottom: 1rem;
    padding: 0.5rem;
    background-color: var(--aac-bg-primary);
  }

  .aac-table-stack td {
    border: none;
    position: relative;
    padding-left: 50% !important;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }

  .aac-table-stack td:before {
    content: attr(data-label) ": ";
    position: absolute;
    left: 0.5rem;
    width: 45%;
    padding-right: 10px;
    white-space: nowrap;
    font-weight: 600;
    color: var(--aac-text-secondary);
  }
}

/* Table Colors */
.aac-table-primary {
  background-color: var(--aac-primary);
  color: var(--aac-white);
}
.aac-table-secondary {
  background-color: var(--aac-secondary);
  color: var(--aac-white);
}
.aac-table-success {
  background-color: var(--aac-success);
  color: var(--aac-white);
}
.aac-table-warning {
  background-color: var(--aac-warning);
  color: var(--aac-dark);
}
.aac-table-danger {
  background-color: var(--aac-danger);
  color: var(--aac-white);
}
.aac-table-light {
  background-color: var(--aac-light);
  color: var(--aac-dark);
}
.aac-table-dark {
  background-color: var(--aac-dark);
  color: var(--aac-white);
}

/* Table Row Colors */
.aac-table tbody tr.aac-table-primary {
  background-color: var(--aac-primary);
  color: var(--aac-white);
}
.aac-table tbody tr.aac-table-secondary {
  background-color: var(--aac-secondary);
  color: var(--aac-white);
}
.aac-table tbody tr.aac-table-success {
  background-color: var(--aac-success);
  color: var(--aac-white);
}
.aac-table tbody tr.aac-table-warning {
  background-color: var(--aac-warning);
  color: var(--aac-dark);
}
.aac-table tbody tr.aac-table-danger {
  background-color: var(--aac-danger);
  color: var(--aac-white);
}
.aac-table tbody tr.aac-table-light {
  background-color: var(--aac-light);
  color: var(--aac-dark);
}
.aac-table tbody tr.aac-table-dark {
  background-color: var(--aac-dark);
  color: var(--aac-white);
}

/* Table Cell Colors */
.aac-table td.aac-table-primary,
.aac-table th.aac-table-primary {
  background-color: var(--aac-primary);
  color: var(--aac-white);
}
.aac-table td.aac-table-secondary,
.aac-table th.aac-table-secondary {
  background-color: var(--aac-secondary);
  color: var(--aac-white);
}
.aac-table td.aac-table-success,
.aac-table th.aac-table-success {
  background-color: var(--aac-success);
  color: var(--aac-white);
}
.aac-table td.aac-table-warning,
.aac-table th.aac-table-warning {
  background-color: var(--aac-warning);
  color: var(--aac-dark);
}
.aac-table td.aac-table-danger,
.aac-table th.aac-table-danger {
  background-color: var(--aac-danger);
  color: var(--aac-white);
}
.aac-table td.aac-table-light,
.aac-table th.aac-table-light {
  background-color: var(--aac-light);
  color: var(--aac-dark);
}
.aac-table td.aac-table-dark,
.aac-table th.aac-table-dark {
  background-color: var(--aac-dark);
  color: var(--aac-white);
}

/* Table Text Alignment */
.aac-table .aac-text-center {
  text-align: center;
}
.aac-table .aac-text-right {
  text-align: right;
}
.aac-table .aac-text-left {
  text-align: left;
}

/* Table Caption */
.aac-table caption {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  color: var(--aac-text-secondary);
  text-align: left;
  caption-side: top;
}

/* Badge Components */
.aac-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 700;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.375rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out;
}

.aac-badge-primary {
  color: var(--aac-white);
  background-color: var(--aac-primary);
}

.aac-badge-secondary {
  color: var(--aac-white);
  background-color: var(--aac-secondary);
}

.aac-badge-success {
  color: var(--aac-white);
  background-color: var(--aac-success);
}

.aac-badge-warning {
  color: var(--aac-dark);
  background-color: var(--aac-warning);
}

.aac-badge-danger {
  color: var(--aac-white);
  background-color: var(--aac-danger);
}

.aac-badge-light {
  color: var(--aac-dark);
  background-color: var(--aac-light);
}

.aac-badge-dark {
  color: var(--aac-white);
  background-color: var(--aac-dark);
}

/* Badge Sizes */
.aac-badge-sm {
  padding: 0.125rem 0.375rem;
  font-size: 0.6875rem;
}

.aac-badge-lg {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
}

/* Modern Header Component */
.aac-header {
  background-color: var(--aac-bg-primary);
  border-bottom: 1px solid var(--aac-border-color);
  padding: 0;
  position: sticky;
  top: 0;
  z-index: 1000;
  transition: all 0.3s ease;
}

.aac-header-shadow {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .aac-header-shadow {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.aac-header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* Header Logo */
.aac-header-logo {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: var(--aac-text-primary);
  font-weight: 700;
  font-size: 1.5rem;
  transition: color 0.3s ease;
}

.aac-header-logo:hover {
  color: var(--aac-primary);
  text-decoration: none;
}

.aac-header-logo img {
  height: 2rem;
  width: auto;
  margin-right: 0.5rem;
}

.aac-header-logo-text {
  font-size: 1.5rem;
  font-weight: 700;
}

/* Header Navigation */
.aac-header-nav {
  display: flex;
  align-items: center;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 2rem;
}

.aac-header-nav-item {
  position: relative;
}

.aac-header-nav-link {
  display: flex;
  align-items: center;
  padding: 0.5rem 0;
  color: var(--aac-text-primary);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
  position: relative;
}

.aac-header-nav-link:hover,
.aac-header-nav-link.aac-active {
  color: var(--aac-primary);
  text-decoration: none;
}

.aac-header-nav-link::after {
  content: "";
  position: absolute;
  bottom: -0.5rem;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--aac-primary);
  transition: width 0.3s ease;
}

.aac-header-nav-link:hover::after,
.aac-header-nav-link.aac-active::after {
  width: 100%;
}

/* Header Actions */
.aac-header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.aac-header-search {
  position: relative;
  display: flex;
  align-items: center;
}

.aac-header-search input {
  padding: 0.5rem 1rem;
  border: 1px solid var(--aac-border-color);
  border-radius: 1.5rem;
  background-color: var(--aac-bg-secondary);
  color: var(--aac-text-primary);
  font-size: 0.875rem;
  width: 200px;
  transition: all 0.3s ease;
}

.aac-header-search input:focus {
  outline: none;
  border-color: var(--aac-primary);
  width: 250px;
}

/* Mobile Menu Button */
.aac-header-menu-btn {
  display: none;
  background: none;
  border: none;
  color: var(--aac-text-primary);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.25rem;
  transition: background-color 0.3s ease;
}

.aac-header-menu-btn:hover {
  background-color: var(--aac-bg-secondary);
}

/* Header Theme Toggle */
.aac-header-theme-toggle {
  background: none;
  border: 1px solid var(--aac-border-color);
  border-radius: 50%;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--aac-text-primary);
}

.aac-header-theme-toggle:hover {
  background-color: var(--aac-bg-secondary);
  transform: scale(1.1);
}

/* Header User Menu */
.aac-header-user {
  position: relative;
}

.aac-header-user-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  color: var(--aac-text-primary);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.5rem;
  transition: background-color 0.3s ease;
}

.aac-header-user-btn:hover {
  background-color: var(--aac-bg-secondary);
}

.aac-header-user-avatar {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--aac-border-color);
}

/* Header Dropdown Menu */
.aac-header-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: var(--aac-bg-primary);
  border: 1px solid var(--aac-border-color);
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  min-width: 200px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  z-index: 1100;
}

.aac-header-dropdown.aac-show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.aac-header-dropdown-item {
  display: block;
  padding: 0.75rem 1rem;
  color: var(--aac-text-primary);
  text-decoration: none;
  transition: background-color 0.3s ease;
  border-bottom: 1px solid var(--aac-border-color);
}

.aac-header-dropdown-item:last-child {
  border-bottom: none;
}

.aac-header-dropdown-item:hover {
  background-color: var(--aac-bg-secondary);
  color: var(--aac-text-primary);
  text-decoration: none;
}

/* Mobile Header Adjustments */
@media (max-width: 991.98px) {
  .aac-header-container {
    padding: 1rem;
  }

  .aac-header-nav {
    display: none;
  }

  .aac-header-menu-btn {
    display: block;
  }

  .aac-header-search {
    display: none;
  }

  .aac-header-actions {
    gap: 0.5rem;
  }
}

@media (max-width: 575.98px) {
  .aac-header-container {
    padding: 0.75rem;
  }

  .aac-header-logo-text {
    font-size: 1.25rem;
  }

  .aac-header-theme-toggle {
    width: 2rem;
    height: 2rem;
    font-size: 0.875rem;
  }
}

/* Mobile Navigation Slide Menu */
.aac-mobile-nav {
  position: fixed;
  top: 0;
  left: -100%;
  width: 280px;
  height: 100vh;
  background-color: var(--aac-bg-primary);
  border-right: 1px solid var(--aac-border-color);
  z-index: 1200;
  transition: left 0.3s ease;
  overflow-y: auto;
}

.aac-mobile-nav.aac-active {
  left: 0;
}

.aac-mobile-nav-header {
  padding: 1rem;
  border-bottom: 1px solid var(--aac-border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.aac-mobile-nav-logo {
  display: flex;
  align-items: center;
  color: var(--aac-text-primary);
  text-decoration: none;
  font-weight: 700;
  font-size: 1.25rem;
}

.aac-mobile-nav-close {
  background: none;
  border: none;
  color: var(--aac-text-primary);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.25rem;
}

.aac-mobile-nav-menu {
  padding: 1rem 0;
}

.aac-mobile-nav-item {
  display: block;
  padding: 0.75rem 1.5rem;
  color: var(--aac-text-primary);
  text-decoration: none;
  transition: background-color 0.3s ease;
  border-bottom: 1px solid var(--aac-border-color);
}

.aac-mobile-nav-item:last-child {
  border-bottom: none;
}

.aac-mobile-nav-item:hover,
.aac-mobile-nav-item.aac-active {
  background-color: var(--aac-bg-secondary);
  color: var(--aac-primary);
  text-decoration: none;
}

.aac-mobile-nav-search {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--aac-border-color);
}

.aac-mobile-nav-search input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--aac-border-color);
  border-radius: 0.5rem;
  background-color: var(--aac-bg-secondary);
  color: var(--aac-text-primary);
}

.aac-mobile-nav-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1150;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.aac-mobile-nav-overlay.aac-active {
  opacity: 1;
  visibility: visible;
}

/* Responsive Breakpoints */
@media (min-width: 640px) {
  .aac-sm-col-1 {
    flex: 0 0 8.333333%;
    max-width: 8.333333%;
  }
  .aac-sm-col-2 {
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
  }
  .aac-sm-col-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .aac-sm-col-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }
  .aac-sm-col-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .aac-sm-col-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

@media (min-width: 768px) {
  .aac-md-col-1 {
    flex: 0 0 8.333333%;
    max-width: 8.333333%;
  }
  .aac-md-col-2 {
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
  }
  .aac-md-col-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .aac-md-col-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }
  .aac-md-col-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .aac-md-col-8 {
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
  }
  .aac-md-col-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

@media (min-width: 1024px) {
  .aac-lg-col-1 {
    flex: 0 0 8.333333%;
    max-width: 8.333333%;
  }
  .aac-lg-col-2 {
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
  }
  .aac-lg-col-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .aac-lg-col-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }
  .aac-lg-col-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .aac-lg-col-8 {
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
  }
  .aac-lg-col-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }
  .aac-lg-col-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
}
