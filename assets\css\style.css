/*
Theme Name: FlexFrame WordPress Theme
Description: A flexible WordPress theme with mega menu and table of contents support
Version: 1.0
Author: FlexFrame
Text Domain: flexframe
*/

/* Use framework color variables */
:root {
  /* WordPress specific color mappings using AAC framework colors */
  --wp-primary: var(--aac-primary);
  --wp-secondary: var(--aac-secondary);
  --wp-success: var(--aac-success);
  --wp-warning: var(--aac-warning);
  --wp-danger: var(--aac-danger);
  --wp-light: var(--aac-light);
  --wp-dark: var(--aac-dark);
  --wp-white: var(--aac-white);

  /* Background colors from framework */
  --wp-bg-primary: var(--aac-bg-primary);
  --wp-bg-secondary: var(--aac-bg-secondary);
  --wp-text-primary: var(--aac-text-primary);
  --wp-text-secondary: var(--aac-text-secondary);
  --wp-border-color: var(--aac-border-color);

  /* Gray scale from framework */
  --wp-gray-50: var(--aac-gray-50);
  --wp-gray-100: var(--aac-gray-100);
  --wp-gray-200: var(--aac-gray-200);
  --wp-gray-300: var(--aac-gray-300);
  --wp-gray-400: var(--aac-gray-400);
  --wp-gray-500: var(--aac-gray-500);
  --wp-gray-600: var(--aac-gray-600);
  --wp-gray-700: var(--aac-gray-700);
  --wp-gray-800: var(--aac-gray-800);
  --wp-gray-900: var(--aac-gray-900);

  /* Pastel colors from framework */
  --wp-pastel-pink: var(--aac-pastel-pink);
  --wp-pastel-blue: var(--aac-pastel-blue);
  --wp-pastel-mint: var(--aac-pastel-mint);
  --wp-pastel-peach: var(--aac-pastel-peach);
  --wp-pastel-lavender: var(--aac-pastel-lavender);
  --wp-pastel-coral: var(--aac-pastel-coral);
  --wp-pastel-sage: var(--aac-pastel-sage);
  --wp-pastel-lilac: var(--aac-pastel-lilac);
  --wp-pastel-sky: var(--aac-pastel-sky);
  --wp-pastel-cream: var(--aac-pastel-cream);
  --wp-pastel-rose: var(--aac-pastel-rose);
  --wp-pastel-aqua: var(--aac-pastel-aqua);
  --wp-pastel-lemon: var(--aac-pastel-lemon);
  --wp-pastel-mauve: var(--aac-pastel-mauve);
  --wp-pastel-seafoam: var(--aac-pastel-seafoam);

  /* Dynamic shadow colors */
  --wp-shadow-light: var(--aac-gray-200);
  --wp-shadow-medium: var(--aac-gray-300);
  --wp-shadow-dark: var(--aac-gray-400);

  /* Dynamic hover states */
  --wp-hover-primary: var(--aac-secondary);
  --wp-hover-secondary: var(--aac-gray-600);
  --wp-hover-bg: var(--aac-gray-100);

  /* Dynamic focus states */
  --wp-focus-color: var(--aac-primary);
  --wp-focus-bg: var(--aac-pastel-blue);

  /* Dynamic error and success states */
  --wp-error: var(--aac-danger);
  --wp-success-color: var(--aac-success);
  --wp-warning-color: var(--aac-warning);
}

/* Dark theme overrides */
[data-theme="dark"] {
  --wp-shadow-light: var(--aac-gray-800);
  --wp-shadow-medium: var(--aac-gray-700);
  --wp-shadow-dark: var(--aac-gray-600);
  --wp-hover-bg: var(--aac-gray-800);
  --wp-focus-bg: var(--aac-gray-700);
}

/* WordPress Core Styles */
.alignnone {
  margin: 5px 20px 20px 0;
}

.aligncenter,
div.aligncenter {
  display: block;
  margin: 5px auto 5px auto;
}

.alignright {
  float: right;
  margin: 5px 0 20px 20px;
}

.alignleft {
  float: left;
  margin: 5px 20px 20px 0;
}

a img.alignright {
  float: right;
  margin: 5px 0 20px 20px;
}

a img.alignnone {
  margin: 5px 20px 20px 0;
}

a img.alignleft {
  float: left;
  margin: 5px 20px 20px 0;
}

a img.aligncenter {
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.wp-caption {
  background: var(--wp-white);
  border: 1px solid var(--wp-gray-200);
  max-width: 96%;
  padding: 5px 3px 10px;
  text-align: center;
}

.wp-caption.alignnone {
  margin: 5px 20px 20px 0;
}

.wp-caption.alignleft {
  margin: 5px 20px 20px 0;
}

.wp-caption.alignright {
  margin: 5px 0 20px 20px;
}

.wp-caption img {
  border: 0 none;
  height: auto;
  margin: 0;
  max-width: 98.5%;
  padding: 0;
  width: auto;
}

.wp-caption p.wp-caption-text {
  font-size: 11px;
  line-height: 17px;
  margin: 0;
  padding: 0 4px 5px;
  color: var(--wp-text-secondary);
}

/* WordPress Gallery Styles */
.gallery {
  margin: auto;
}

.gallery .gallery-item {
  float: left;
  margin-top: 10px;
  text-align: center;
}

.gallery img {
  border: 2px solid var(--wp-gray-300);
}

.gallery .gallery-caption {
  margin-left: 0;
}

.gallery dl {
  margin: 0;
}

.gallery img {
  border: 10px solid var(--wp-gray-100);
}

.gallery br + br {
  display: none;
}

/* WordPress Comment Styles */
.comments-area {
  margin: 2rem 0;
  padding: 2rem;
  background: var(--wp-bg-secondary);
  border-radius: 8px;
}

.comments-title {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  color: var(--wp-text-primary);
  border-bottom: 2px solid var(--wp-border-color);
  padding-bottom: 0.5rem;
}

.comment-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.comment {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: var(--wp-bg-primary);
  border-radius: 6px;
  box-shadow: 0 2px 4px var(--wp-shadow-light);
  border: 1px solid var(--wp-border-color);
}

.comment-author {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.comment-author .avatar {
  border-radius: 50%;
  margin-right: 1rem;
}

.comment-author .fn {
  font-weight: 600;
  color: var(--wp-text-primary);
  text-decoration: none;
}

.comment-meta {
  font-size: 0.875rem;
  color: var(--wp-text-secondary);
  margin-bottom: 1rem;
}

.comment-content {
  line-height: 1.6;
  color: var(--wp-text-primary);
}

.comment-content p {
  margin-bottom: 1rem;
}

.reply {
  margin-top: 1rem;
}

.comment-reply-link {
  background: var(--wp-primary);
  color: var(--wp-white);
  padding: 0.5rem 1rem;
  border-radius: 4px;
  text-decoration: none;
  font-size: 0.875rem;
  transition: background 0.3s ease;
}

.comment-reply-link:hover {
  background: var(--wp-hover-primary);
  text-decoration: none;
}

/* Nested Comments */
.children {
  list-style: none;
  margin: 1.5rem 0 0 2rem;
  padding: 0;
}

.children .comment {
  border-left: 3px solid var(--wp-primary);
  margin-left: 1rem;
}

/* Comment Form */
.comment-respond {
  margin-top: 2rem;
  padding: 2rem;
  background: var(--wp-bg-primary);
  border-radius: 6px;
  box-shadow: 0 2px 4px var(--wp-shadow-light);
  border: 1px solid var(--wp-border-color);
}

.comment-reply-title {
  font-size: 1.25rem;
  margin-bottom: 1.5rem;
  color: var(--wp-text-primary);
}

.comment-form-comment label,
.comment-form-author label,
.comment-form-email label,
.comment-form-url label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--wp-text-primary);
}

.comment-form-comment textarea,
.comment-form-author input,
.comment-form-email input,
.comment-form-url input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--wp-border-color);
  border-radius: 4px;
  font-size: 1rem;
  background: var(--wp-bg-primary);
  color: var(--wp-text-primary);
  transition: border-color 0.3s ease;
}

.comment-form-comment textarea:focus,
.comment-form-author input:focus,
.comment-form-email input:focus,
.comment-form-url input:focus {
  outline: none;
  border-color: var(--wp-focus-color);
  background: var(--wp-focus-bg);
}

.form-submit {
  margin-top: 1rem;
}

.submit {
  background: var(--wp-primary);
  color: var(--wp-white);
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  transition: background 0.3s ease;
}

.submit:hover {
  background: var(--wp-hover-primary);
}

/* Mega Menu Styles */
.mega-menu {
  position: relative;
  z-index: 1000;
}

.mega-menu-container {
  position: relative;
}

.mega-menu-toggle {
  display: none;
  background: var(--wp-dark);
  color: var(--wp-white);
  border: none;
  padding: 1rem;
  cursor: pointer;
  width: 100%;
  text-align: left;
  font-size: 1rem;
}

.mega-menu-nav {
  background: var(--wp-dark);
  box-shadow: 0 2px 5px var(--wp-shadow-medium);
  border-bottom: 1px solid var(--wp-border-color);
}

.mega-menu-list {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  justify-content: center;
}

.mega-menu-item {
  position: relative;
}

.mega-menu-link {
  display: block;
  padding: 1rem 1.5rem;
  color: var(--wp-white);
  text-decoration: none;
  transition: background 0.3s ease;
  font-weight: 500;
}

.mega-menu-link:hover,
.mega-menu-item:hover > .mega-menu-link {
  background: var(--wp-hover-secondary);
  color: var(--wp-white);
  text-decoration: none;
}

/* Mega Menu Dropdown */
.mega-menu-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  background: var(--wp-bg-primary);
  border: 1px solid var(--wp-border-color);
  border-radius: 6px;
  box-shadow: 0 5px 15px var(--wp-shadow-dark);
  min-width: 800px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  z-index: 1001;
}

.mega-menu-item:hover .mega-menu-dropdown {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.mega-menu-content {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
  padding: 2rem;
}

.mega-menu-column {
  min-height: 200px;
}

.mega-menu-column-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--wp-text-primary);
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--wp-primary);
}

.mega-menu-column-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.mega-menu-column-item {
  margin-bottom: 0.5rem;
}

.mega-menu-column-link {
  color: var(--wp-text-secondary);
  text-decoration: none;
  padding: 0.25rem 0;
  display: block;
  transition: color 0.3s ease;
}

.mega-menu-column-link:hover {
  color: var(--wp-primary);
  text-decoration: none;
}

/* Table of Contents Styles */
.table-of-contents {
  background: var(--wp-bg-secondary);
  border: 1px solid var(--wp-border-color);
  border-radius: 6px;
  padding: 1.5rem;
  margin: 2rem 0;
  max-width: 300px;
  float: right;
  margin-left: 2rem;
}

.toc-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--wp-text-primary);
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--wp-primary);
}

.toc-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.toc-item {
  margin-bottom: 0.5rem;
  padding-left: 1rem;
  position: relative;
}

.toc-item::before {
  content: "→";
  position: absolute;
  left: 0;
  color: var(--wp-primary);
  font-weight: bold;
  transition: color 0.3s ease;
}

.toc-item:hover::before {
  color: var(--wp-hover-primary);
}

.toc-link {
  color: var(--wp-text-secondary);
  text-decoration: none;
  font-size: 0.875rem;
  line-height: 1.4;
  transition: color 0.3s ease;
}

.toc-link:hover {
  color: var(--wp-primary);
  text-decoration: none;
}

.toc-item.toc-level-2 {
  padding-left: 2rem;
}

.toc-item.toc-level-3 {
  padding-left: 3rem;
}

.toc-item.toc-level-4 {
  padding-left: 4rem;
}

/* Sticky Table of Contents */
.toc-sticky {
  position: sticky;
  top: 2rem;
}

/* Mobile Table of Contents */
@media (max-width: 768px) {
  .table-of-contents {
    float: none;
    margin: 2rem 0;
    max-width: 100%;
  }
}

/* RTL (Right-to-Left) Support */
[dir="rtl"] .alignleft {
  float: right;
  margin: 5px 0 20px 20px;
}

[dir="rtl"] .alignright {
  float: left;
  margin: 5px 20px 20px 0;
}

[dir="rtl"] a img.alignleft {
  float: right;
  margin: 5px 0 20px 20px;
}

[dir="rtl"] a img.alignright {
  float: left;
  margin: 5px 20px 20px 0;
}

[dir="rtl"] .wp-caption.alignleft {
  margin: 5px 0 20px 20px;
}

[dir="rtl"] .wp-caption.alignright {
  margin: 5px 20px 20px 0;
}

[dir="rtl"] .comment-author .avatar {
  margin-right: 0;
  margin-left: 1rem;
}

[dir="rtl"] .children {
  margin: 1.5rem 2rem 0 0;
}

[dir="rtl"] .children .comment {
  border-left: none;
  border-right: 3px solid var(--wp-primary);
  margin-left: 0;
  margin-right: 1rem;
}

/* RTL Text Alignment */
[dir="rtl"] body,
[dir="rtl"] .comment-content,
[dir="rtl"] .wp-caption-text {
  text-align: right;
}

[dir="rtl"] .comment-form-comment label,
[dir="rtl"] .comment-form-author label,
[dir="rtl"] .comment-form-email label,
[dir="rtl"] .comment-form-url label {
  text-align: right;
}

/* RTL Menu Support */
[dir="rtl"] .mega-menu-list {
  direction: rtl;
}

[dir="rtl"] .mega-menu-dropdown {
  left: auto;
  right: 0;
}

[dir="rtl"] .mega-menu-content {
  direction: rtl;
}

[dir="rtl"] .mega-menu-column-title {
  text-align: right;
}

/* RTL Table of Contents */
[dir="rtl"] .table-of-contents {
  direction: rtl;
  text-align: right;
  float: left;
  margin-right: 2rem;
  margin-left: 0;
}

[dir="rtl"] .toc-list {
  padding-left: 0;
  padding-right: 0;
}

[dir="rtl"] .toc-item {
  text-align: right;
  padding-left: 0;
  padding-right: 1rem;
}

[dir="rtl"] .toc-item::before {
  content: "←";
  left: auto;
  right: 0;
}

[dir="rtl"] .toc-item.toc-level-2 {
  padding-left: 0;
  padding-right: 2rem;
}

[dir="rtl"] .toc-item.toc-level-3 {
  padding-left: 0;
  padding-right: 3rem;
}

[dir="rtl"] .toc-item.toc-level-4 {
  padding-left: 0;
  padding-right: 4rem;
}

/* RTL Form Elements */
[dir="rtl"] .comment-form-comment textarea,
[dir="rtl"] .comment-form-author input,
[dir="rtl"] .comment-form-email input,
[dir="rtl"] .comment-form-url input {
  text-align: right;
  direction: rtl;
}

/* RTL Gallery Support */
[dir="rtl"] .gallery .gallery-item {
  float: right;
}

/* RTL Responsive Adjustments */
@media (max-width: 768px) {
  [dir="rtl"] .mega-menu-dropdown {
    right: 0;
    left: auto;
    min-width: 100%;
  }

  [dir="rtl"] .children {
    margin: 1.5rem 1rem 0 0;
  }

  [dir="rtl"] .table-of-contents {
    float: none;
    margin: 2rem 0;
  }
}

/* WordPress Dynamic State Classes */
.wp-error,
.error {
  color: var(--wp-error);
  background: var(--wp-pastel-coral);
  border: 1px solid var(--wp-error);
  padding: 0.75rem;
  border-radius: 4px;
  margin: 1rem 0;
}

.wp-success,
.success {
  color: var(--wp-success-color);
  background: var(--wp-pastel-mint);
  border: 1px solid var(--wp-success-color);
  padding: 0.75rem;
  border-radius: 4px;
  margin: 1rem 0;
}

.wp-warning,
.warning {
  color: var(--wp-warning-color);
  background: var(--wp-pastel-yellow);
  border: 1px solid var(--wp-warning-color);
  padding: 0.75rem;
  border-radius: 4px;
  margin: 1rem 0;
}

/* WordPress Dynamic Button Variants */
.wp-button-primary {
  background: var(--wp-primary);
  color: var(--wp-white);
  border: 1px solid var(--wp-primary);
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.wp-button-primary:hover {
  background: var(--wp-hover-primary);
  border-color: var(--wp-hover-primary);
}

.wp-button-secondary {
  background: var(--wp-secondary);
  color: var(--wp-white);
  border: 1px solid var(--wp-secondary);
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.wp-button-secondary:hover {
  background: var(--wp-hover-secondary);
  border-color: var(--wp-hover-secondary);
}

/* WordPress Dynamic Link States */
a {
  color: var(--wp-primary);
  transition: color 0.3s ease;
}

a:hover {
  color: var(--wp-hover-primary);
}

a:focus {
  color: var(--wp-focus-color);
  outline: 2px solid var(--wp-focus-bg);
  outline-offset: 2px;
}

/* WordPress Dynamic Form Validation */
.form-invalid input,
.form-invalid textarea,
.form-invalid select {
  border-color: var(--wp-error);
  background: var(--wp-pastel-coral);
}

.form-valid input,
.form-valid textarea,
.form-valid select {
  border-color: var(--wp-success-color);
  background: var(--wp-pastel-mint);
}

/* WordPress Dynamic Highlight Classes */
.highlight {
  background: var(--wp-pastel-yellow);
  color: var(--wp-text-primary);
  padding: 0.25rem 0.5rem;
  border-radius: 3px;
}

.highlight-primary {
  background: var(--wp-pastel-blue);
  color: var(--wp-primary);
}

.highlight-success {
  background: var(--wp-pastel-mint);
  color: var(--wp-success-color);
}

.highlight-warning {
  background: var(--wp-pastel-peach);
  color: var(--wp-warning-color);
}

.highlight-error {
  background: var(--wp-pastel-coral);
  color: var(--wp-error);
}

.comment-reply-link:hover {
  background: #005a87;
  text-decoration: none;
}

/* RTL (Right-to-Left) Support */
[dir="rtl"] .alignleft {
  float: right;
  margin: 5px 0 20px 20px;
}

[dir="rtl"] .alignright {
  float: left;
  margin: 5px 20px 20px 0;
}

[dir="rtl"] a img.alignleft {
  float: right;
  margin: 5px 0 20px 20px;
}

[dir="rtl"] a img.alignright {
  float: left;
  margin: 5px 20px 20px 0;
}

[dir="rtl"] .wp-caption.alignleft {
  margin: 5px 0 20px 20px;
}

[dir="rtl"] .wp-caption.alignright {
  margin: 5px 20px 20px 0;
}

[dir="rtl"] .comment-author .avatar {
  margin-right: 0;
  margin-left: 1rem;
}

[dir="rtl"] .children {
  margin: 1.5rem 2rem 0 0;
}

[dir="rtl"] .children .comment {
  border-left: none;
  border-right: 3px solid var(--wp-primary);
  margin-left: 0;
  margin-right: 1rem;
}

/* RTL Text Alignment */
[dir="rtl"] body,
[dir="rtl"] .comment-content,
[dir="rtl"] .wp-caption-text {
  text-align: right;
}

[dir="rtl"] .comment-form-comment label,
[dir="rtl"] .comment-form-author label,
[dir="rtl"] .comment-form-email label,
[dir="rtl"] .comment-form-url label {
  text-align: right;
}

/* RTL Menu Support */
[dir="rtl"] .mega-menu-list {
  direction: rtl;
}

[dir="rtl"] .mega-menu-dropdown {
  left: auto;
  right: 0;
}

[dir="rtl"] .mega-menu-content {
  direction: rtl;
}

[dir="rtl"] .mega-menu-column-title {
  text-align: right;
}

/* RTL Table of Contents */
[dir="rtl"] .table-of-contents {
  direction: rtl;
  text-align: right;
}

[dir="rtl"] .toc-list {
  padding-left: 0;
  padding-right: 1.5rem;
}

[dir="rtl"] .toc-item {
  text-align: right;
}

[dir="rtl"] .toc-link {
  text-align: right;
}

/* Nested Comments RTL */
[dir="rtl"] .comment-list .comment-list {
  margin-right: 2rem;
  margin-left: 0;
}

/* Form Elements RTL */
[dir="rtl"] .comment-form-comment textarea,
[dir="rtl"] .comment-form-author input,
[dir="rtl"] .comment-form-email input,
[dir="rtl"] .comment-form-url input {
  text-align: right;
  direction: rtl;
}

/* Gallery RTL Support */
[dir="rtl"] .gallery .gallery-item {
  float: right;
}

/* WordPress Core RTL Overrides */
[dir="rtl"] .alignleft {
  float: right;
  margin: 5px 0 20px 20px;
}

[dir="rtl"] .alignright {
  float: left;
  margin: 5px 20px 20px 0;
}

/* RTL Responsive Adjustments */
@media (max-width: 768px) {
  [dir="rtl"] .mega-menu-dropdown {
    right: 0;
    left: auto;
    min-width: 100%;
  }

  [dir="rtl"] .children {
    margin: 1.5rem 1rem 0 0;
  }
}

.comment-reply-link:hover {
  background: var(--comment-primary-hover, #005a87);
  text-decoration: none;
}

/* Nested Comments */
.children {
  list-style: none;
  margin: 1.5rem 0 0 2rem;
  padding: 0;
}

.children .comment {
  border-left: 3px solid var(--comment-primary, #007cba);
  margin-left: 1rem;
}

/* Comment Form */
.comment-respond {
  margin-top: 2rem;
  padding: 2rem;
  background: var(--comment-form-bg, #fff);
  border-radius: 6px;
  box-shadow: 0 2px 4px var(--comment-shadow, rgba(0, 0, 0, 0.1));
}

.comment-reply-title {
  font-size: 1.25rem;
  margin-bottom: 1.5rem;
  color: var(--comment-text-primary, #333);
}

.comment-form-comment label,
.comment-form-author label,
.comment-form-email label,
.comment-form-url label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--comment-text-primary, #333);
}

.comment-form-comment textarea,
.comment-form-author input,
.comment-form-email input,
.comment-form-url input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--comment-border, #ddd);
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.comment-form-comment textarea:focus,
.comment-form-author input:focus,
.comment-form-email input:focus,
.comment-form-url input:focus {
  outline: none;
  border-color: var(--comment-primary, #007cba);
}

.form-submit {
  margin-top: 1rem;
}

.submit {
  background: var(--comment-primary, #007cba);
  color: var(--comment-button-text, #fff);
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  transition: background 0.3s ease;
}

.submit:hover {
  background: var(--comment-primary-hover, #005a87);
}

/* Mega Menu Mobile Responsive */
@media (max-width: 768px) {
  .mega-menu-toggle {
    display: block;
  }

  .mega-menu-list {
    display: none;
    flex-direction: column;
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background: var(--wp-dark);
    border-top: 1px solid var(--wp-border-color);
  }

  .mega-menu-list.active {
    display: flex;
  }

  .mega-menu-dropdown {
    position: static;
    opacity: 1;
    visibility: visible;
    transform: none;
    min-width: auto;
    box-shadow: none;
    border: none;
    border-radius: 0;
  }

  .mega-menu-content {
    grid-template-columns: 1fr;
    padding: 1rem;
  }
}

/* Mega Menu Dynamic Color Variants */
.mega-menu-primary {
  background: var(--wp-primary);
}

.mega-menu-primary .mega-menu-link {
  color: var(--wp-white);
}

.mega-menu-primary .mega-menu-link:hover {
  background: var(--wp-hover-primary);
}

.mega-menu-secondary {
  background: var(--wp-secondary);
}

.mega-menu-secondary .mega-menu-link {
  color: var(--wp-white);
}

.mega-menu-secondary .mega-menu-link:hover {
  background: var(--wp-hover-secondary);
}

.mega-menu-light {
  background: var(--wp-light);
  border-bottom: 1px solid var(--wp-border-color);
}

.mega-menu-light .mega-menu-link {
  color: var(--wp-text-primary);
}

.mega-menu-light .mega-menu-link:hover {
  background: var(--wp-hover-bg);
  color: var(--wp-primary);
}

/* Mega Menu Column Variants */
.mega-menu-column-featured {
  background: var(--wp-pastel-blue);
  padding: 1rem;
  border-radius: 6px;
  border: 1px solid var(--wp-primary);
}

.mega-menu-column-featured .mega-menu-column-title {
  color: var(--wp-primary);
  border-bottom-color: var(--wp-primary);
}

.mega-menu-column-highlight {
  background: var(--wp-pastel-mint);
  padding: 1rem;
  border-radius: 6px;
  border: 1px solid var(--wp-success-color);
}

.mega-menu-column-highlight .mega-menu-column-title {
  color: var(--wp-success-color);
  border-bottom-color: var(--wp-success-color);
}

/* Mega Menu Interactive States */
.mega-menu-column-link:focus {
  color: var(--wp-focus-color);
  background: var(--wp-focus-bg);
  outline: 2px solid var(--wp-focus-color);
  outline-offset: 2px;
  border-radius: 3px;
}

.mega-menu-column-link:active {
  color: var(--wp-white);
  background: var(--wp-primary);
}

/* Mega Menu Badge/Label Support */
.mega-menu-badge {
  display: inline-block;
  background: var(--wp-primary);
  color: var(--wp-white);
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  margin-left: 0.5rem;
  font-weight: 600;
}

.mega-menu-badge-new {
  background: var(--wp-success-color);
}

.mega-menu-badge-hot {
  background: var(--wp-error);
}

.mega-menu-badge-sale {
  background: var(--wp-warning-color);
  color: var(--wp-dark);
}

/* Mega Menu Icons Support */
.mega-menu-icon {
  margin-right: 0.5rem;
  color: var(--wp-primary);
  transition: color 0.3s ease;
}

.mega-menu-link:hover .mega-menu-icon {
  color: var(--wp-white);
}

.mega-menu-column-link .mega-menu-icon {
  color: var(--wp-text-secondary);
  font-size: 0.875rem;
}

.mega-menu-column-link:hover .mega-menu-icon {
  color: var(--wp-primary);
}

/* RTL Mega Menu Badge Support */
[dir="rtl"] .mega-menu-badge {
  margin-left: 0;
  margin-right: 0.5rem;
}

[dir="rtl"] .mega-menu-icon {
  margin-right: 0;
  margin-left: 0.5rem;
}

/* Mega Menu Animation Enhancements */
.mega-menu-column-link {
  position: relative;
  overflow: hidden;
}

.mega-menu-column-link::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    var(--wp-pastel-blue),
    transparent
  );
  transition: left 0.5s ease;
}

.mega-menu-column-link:hover::before {
  left: 100%;
}

/* Mega Menu Search Integration */
.mega-menu-search {
  padding: 1rem;
  border-bottom: 1px solid var(--wp-border-color);
  background: var(--wp-bg-secondary);
}

.mega-menu-search input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--wp-border-color);
  border-radius: 25px;
  background: var(--wp-bg-primary);
  color: var(--wp-text-primary);
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.mega-menu-search input:focus {
  border-color: var(--wp-focus-color);
  background: var(--wp-focus-bg);
  outline: none;
}

.mega-menu-search input::placeholder {
  color: var(--wp-text-secondary);
}
