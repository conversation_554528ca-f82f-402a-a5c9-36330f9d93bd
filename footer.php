<!-- Footer -->
<footer class="aac-bg-secondary aac-text-center aac-p-8 aac-mt-8">
    <div class="aac-container aac-container-xl">
        <div class="aac-row">
            <div class="aac-col-12 aac-md-col-4">
                <h3 class="aac-h3"><?php bloginfo('name'); ?></h3>
                <p class="aac-text-secondary"><?php bloginfo('description'); ?></p>
            </div>

            <div class="aac-col-12 aac-md-col-4">
                <h4 class="aac-h4"><?php _e('Quick Links', 'informa'); ?></h4>
                <?php
                wp_nav_menu(array(
                    'theme_location' => 'footer',
                    'container' => false,
                    'menu_class' => 'aac-list',
                    'fallback_cb' => false
                ));
                ?>
            </div>

            <div class="aac-col-12 aac-md-col-4">
                <?php if (is_active_sidebar('footer-widgets')) : ?>
                <?php dynamic_sidebar('footer-widgets'); ?>
                <?php endif; ?>
            </div>
        </div>

        <div class="aac-text-center aac-mt-4 aac-pt-4" style="border-top: 1px solid var(--aac-border-color);">
            <p class="aac-text-secondary aac-text-sm">
                &copy; <?php echo date('Y'); ?> <?php bloginfo('name'); ?>.
                <?php _e('All rights reserved.', 'informa'); ?>
            </p>
        </div>
    </div>
</footer>

<script>
// Theme management
let currentTheme = localStorage.getItem('aac-theme') || 'light';
document.documentElement.setAttribute('data-theme', currentTheme);
updateThemeIcon();

function toggleTheme() {
    currentTheme = currentTheme === 'light' ? 'dark' : 'light';
    document.documentElement.setAttribute('data-theme', currentTheme);
    localStorage.setItem('aac-theme', currentTheme);
    updateThemeIcon();
}

function updateThemeIcon() {
    const icons = document.querySelectorAll('#theme-icon, #header-theme-icon');
    icons.forEach(icon => {
        icon.textContent = currentTheme === 'dark' ? '☀️' : '🌙';
    });
}

// Mobile Navigation Functions
function openMobileNav() {
    const mobileNav = document.getElementById('mobileNav');
    const overlay = document.getElementById('mobileNavOverlay');
    if (mobileNav && overlay) {
        mobileNav.classList.add('aac-active');
        overlay.classList.add('aac-active');
    }
}

function closeMobileNav() {
    const mobileNav = document.getElementById('mobileNav');
    const overlay = document.getElementById('mobileNavOverlay');
    if (mobileNav && overlay) {
        mobileNav.classList.remove('aac-active');
        overlay.classList.remove('aac-active');
    }
}
</script>

<?php wp_footer(); ?>
</body>

</html>