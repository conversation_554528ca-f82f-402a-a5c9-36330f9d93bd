<?php 
/**
 * Informa functions and definitions
 *
 * @package WordPress
 * @subpackage Informa
 * @since 1.0
 * @version 1.0.0
 */

function Informa_theme_setup(){
    // Add theme support for various WordPress features
    add_theme_support('post-thumbnails');
    add_theme_support('custom-logo');
    add_theme_support('title-tag');
    add_theme_support('custom-background');
    add_theme_support('html5', array('search-form', 'comment-form', 'comment-list', 'gallery', 'caption'));
    
    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'informa'),
        'footer' => __('Footer Menu', 'informa'),
    ));
}
add_action('after_setup_theme', 'Informa_theme_setup');

// Enqueue styles and scripts
function informa_enqueue_styles() {
    // Enqueue main framework CSS
    wp_enqueue_style('aac-framework', get_template_directory_uri() . '/assets/css/base.css', array(), '1.0.0');
    
    // Enqueue theme styles
    wp_enqueue_style('informa-style', get_stylesheet_uri(), array('aac-framework'), '1.0.0');
    
    // Enqueue custom theme CSS
    wp_enqueue_style('informa-custom', get_template_directory_uri() . '/assets/css/style.css', array('aac-framework'), '1.0.0');
}
add_action('wp_enqueue_scripts', 'informa_enqueue_styles');

// Add unique styling classes to body
function informa_body_classes($classes) {
    $classes[] = 'informa-theme';
    $classes[] = 'aac-framework';
    return $classes;
}
add_filter('body_class', 'informa_body_classes');

// Custom Navigation Walker for AAC Framework
class AAC_Nav_Walker extends Walker_Nav_Menu {
    function start_el(&$output, $item, $depth = 0, $args = null, $id = 0) {
        $classes = empty($item->classes) ? array() : (array) $item->classes;
        $active_class = in_array('current-menu-item', $classes) ? ' aac-active' : '';
        
        $output .= '<div class="aac-header-nav-item">';
        $output .= '<a href="' . $item->url . '" class="aac-header-nav-link' . $active_class . '">';
        $output .= $item->title;
        $output .= '</a>';
    }
    
    function end_el(&$output, $item, $depth = 0, $args = null) {
        $output .= '</div>';
    }
}

class AAC_Mobile_Nav_Walker extends Walker_Nav_Menu {
    function start_el(&$output, $item, $depth = 0, $args = null, $id = 0) {
        $classes = empty($item->classes) ? array() : (array) $item->classes;
        $active_class = in_array('current-menu-item', $classes) ? ' aac-active' : '';
        
        $output .= '<a href="' . $item->url . '" class="aac-mobile-nav-item' . $active_class . '">';
        $output .= $item->title;
        $output .= '</a>';
    }
}

// Register sidebar
function informa_widgets_init() {
    register_sidebar(array(
        'name' => __('Footer Widgets', 'informa'),
        'id' => 'footer-widgets',
        'before_widget' => '<div class="aac-card aac-mb-4">',
        'after_widget' => '</div>',
        'before_title' => '<h4 class="aac-h4">',
        'after_title' => '</h4>',
    ));
}
add_action('widgets_init', 'informa_widgets_init');