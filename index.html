<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CloudFlex CSS Framework - Demo</title>
    <link rel="stylesheet" href="cloudflex.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            color: var(--aac-text-primary);
            background-color: var(--aac-bg-primary);
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        .demo-section {
            margin-bottom: 3rem;
            padding: 2rem;
            border: 1px solid var(--aac-border-color);
            border-radius: 0.5rem;
            background-color: var(--aac-bg-secondary);
        }
        
        .demo-grid-item {
            background-color: var(--aac-primary);
            color: var(--aac-white);
            padding: 1rem;
            text-align: center;
            border-radius: 0.25rem;
            margin-bottom: 1rem;
        }
        
        .code-block {
            background-color: var(--aac-gray-100);
            border: 1px solid var(--aac-border-color);
            border-radius: 0.375rem;
            padding: 1rem;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.875rem;
            overflow-x: auto;
            margin-top: 0.5rem;
            position: relative;
        }
        
        [data-theme="dark"] .code-block {
            background-color: var(--aac-gray-800);
            color: var(--aac-gray-100);
        }
        
        .copy-btn {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            background: var(--aac-primary);
            color: var(--aac-white);
            border: none;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }
        
        .copy-btn:hover {
            background: #1d4ed8;
        }

        .aac-theme-toggle {
            position: fixed;
            top: 1rem;
            right: 1rem;
            background: var(--aac-bg-primary);
            border: 1px solid var(--aac-border-color);
            border-radius: 50%;
            width: 3rem;
            height: 3rem;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .aac-theme-toggle:hover {
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <!-- Dark Mode Toggle -->
    <button class="aac-theme-toggle" onclick="toggleTheme()">
        <span id="theme-icon">🌙</span>
    </button>

    <!-- Modern Header Example -->
    <header class="aac-header aac-header-shadow">
        <div class="aac-header-container">
            <!-- Logo -->
            <a href="#" class="aac-header-logo">
                <span class="aac-header-logo-text">CloudFlex</span>
            </a>

            <!-- Desktop Navigation -->
            <nav class="aac-header-nav">
                <div class="aac-header-nav-item">
                    <a href="#" class="aac-header-nav-link aac-active">Home</a>
                </div>
                <div class="aac-header-nav-item">
                    <a href="#" class="aac-header-nav-link">Components</a>
                </div>
                <div class="aac-header-nav-item">
                    <a href="#" class="aac-header-nav-link">Documentation</a>
                </div>
                <div class="aac-header-nav-item">
                    <a href="#" class="aac-header-nav-link">Examples</a>
                </div>
            </nav>

            <!-- Header Actions -->
            <div class="aac-header-actions">
                <!-- Search -->
                <div class="aac-header-search">
                    <input type="text" placeholder="Search...">
                </div>

                <!-- Theme Toggle -->
                <button class="aac-header-theme-toggle" onclick="toggleTheme()">
                    <span id="header-theme-icon">🌙</span>
                </button>

                <!-- User Menu -->
                <div class="aac-header-user">
                    <button class="aac-header-user-btn" onclick="toggleUserDropdown()">
                        <img src="https://picsum.photos/32/32?random=100" alt="User" class="aac-header-user-avatar">
                        <span>John Doe</span>
                    </button>
                    <div class="aac-header-dropdown" id="userDropdown">
                        <a href="#" class="aac-header-dropdown-item">Profile</a>
                        <a href="#" class="aac-header-dropdown-item">Settings</a>
                        <a href="#" class="aac-header-dropdown-item">Help</a>
                        <a href="#" class="aac-header-dropdown-item">Logout</a>
                    </div>
                </div>

                <!-- Mobile Menu Button -->
                <button class="aac-header-menu-btn" onclick="openMobileNav()">☰</button>
            </div>
        </div>
    </header>

    <div class="aac-container aac-container-xl">
        <!-- Hero Section -->
        <section class="aac-text-center aac-p-8">
            <h1 class="aac-h1">CloudFlex CSS Framework</h1>
            <p class="aac-text-lg aac-text-secondary">A modern, lightweight flexbox-based CSS framework with dark mode support</p>
        </section>

        <!-- Modern Header Navigation -->
        <section class="demo-section">
            <h2 class="aac-h2">Modern Header Navigation</h2>
            <p class="aac-mb-4">Complete responsive header with logo, navigation, search, and mobile slide-out menu.</p>
            
            <h3 class="aac-h3">Features</h3>
            <ul class="aac-list">
                <li><strong>Sticky Position:</strong> Header stays at top during scroll</li>
                <li><strong>Responsive Design:</strong> Desktop menu collapses to hamburger on mobile</li>
                <li><strong>Search Bar:</strong> Expandable search input with focus animations</li>
                <li><strong>User Menu:</strong> Dropdown menu with user avatar and actions</li>
                <li><strong>Theme Toggle:</strong> Built-in dark mode toggle button</li>
                <li><strong>Mobile Navigation:</strong> Smooth slide-out menu for mobile devices</li>
                <li><strong>Active States:</strong> Visual indicators for current page</li>
                <li><strong>Hover Effects:</strong> Smooth transitions and visual feedback</li>
            </ul>
            
            <div class="code-block">
                <button class="copy-btn" onclick="copyCode(this, 'header')">Copy</button>
                <pre><code>&lt;!-- Modern Header --&gt;
&lt;header class="aac-header aac-header-shadow"&gt;
  &lt;div class="aac-header-container"&gt;
    &lt;!-- Logo --&gt;
    &lt;a href="#" class="aac-header-logo"&gt;
      &lt;span class="aac-header-logo-text"&gt;Brand Name&lt;/span&gt;
    &lt;/a&gt;

    &lt;!-- Desktop Navigation --&gt;
    &lt;nav class="aac-header-nav"&gt;
      &lt;div class="aac-header-nav-item"&gt;
        &lt;a href="#" class="aac-header-nav-link aac-active"&gt;Home&lt;/a&gt;
      &lt;/div&gt;
      &lt;div class="aac-header-nav-item"&gt;
        &lt;a href="#" class="aac-header-nav-link"&gt;Products&lt;/a&gt;
      &lt;/div&gt;
    &lt;/nav&gt;

    &lt;!-- Header Actions --&gt;
    &lt;div class="aac-header-actions"&gt;
      &lt;div class="aac-header-search"&gt;
        &lt;input type="text" placeholder="Search..."&gt;
      &lt;/div&gt;
      &lt;button class="aac-header-theme-toggle"&gt;🌙&lt;/button&gt;
      &lt;button class="aac-header-menu-btn"&gt;☰&lt;/button&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/header&gt;</code></pre>
            </div>
        </section>

        <!-- Typography Section -->
        <section class="demo-section">
            <h2 class="aac-h2">Typography System</h2>
            <div class="aac-row">
                <div class="aac-col-12 aac-md-col-6">
                    <h1 class="aac-h1">Heading 1</h1>
                    <h2 class="aac-h2">Heading 2</h2>
                    <h3 class="aac-h3">Heading 3</h3>
                    <h4 class="aac-h4">Heading 4</h4>
                    <h5 class="aac-h5">Heading 5</h5>
                    <h6 class="aac-h6">Heading 6</h6>
                </div>
                <div class="aac-col-12 aac-md-col-6">
                    <p class="aac-text-xs">Extra small text (aac-text-xs)</p>
                    <p class="aac-text-sm">Small text (aac-text-sm)</p>
                    <p class="aac-text-base">Base text (aac-text-base)</p>
                    <p class="aac-text-lg">Large text (aac-text-lg)</p>
                    <p class="aac-text-xl">Extra large text (aac-text-xl)</p>
                    <p class="aac-text-2xl">2X large text (aac-text-2xl)</p>
                </div>
            </div>
            <div class="code-block">
                <button class="copy-btn" onclick="copyCode(this, 'typography')">Copy</button>
                <pre><code>&lt;h1 class="aac-h1"&gt;Heading 1&lt;/h1&gt;
&lt;p class="aac-text-lg"&gt;Large text&lt;/p&gt;
&lt;p class="aac-text-secondary"&gt;Secondary text color&lt;/p&gt;</code></pre>
            </div>
        </section>

        <!-- Grid System -->
        <section class="demo-section">
            <h2 class="aac-h2">Flexbox Grid System</h2>
            <h3 class="aac-h3">12-Column Grid</h3>
            <div class="aac-row aac-mb-4">
                <div class="aac-col-12"><div class="demo-grid-item">aac-col-12</div></div>
            </div>
            <div class="aac-row aac-mb-4">
                <div class="aac-col-6"><div class="demo-grid-item">aac-col-6</div></div>
                <div class="aac-col-6"><div class="demo-grid-item">aac-col-6</div></div>
            </div>
            <div class="aac-row aac-mb-4">
                <div class="aac-col-4"><div class="demo-grid-item">aac-col-4</div></div>
                <div class="aac-col-4"><div class="demo-grid-item">aac-col-4</div></div>
                <div class="aac-col-4"><div class="demo-grid-item">aac-col-4</div></div>
            </div>
            
            <div class="code-block">
                <button class="copy-btn" onclick="copyCode(this, 'grid')">Copy</button>
                <pre><code>&lt;div class="aac-row"&gt;
  &lt;div class="aac-col-12 aac-md-col-8 aac-lg-col-9"&gt;Main Content&lt;/div&gt;
  &lt;div class="aac-col-12 aac-md-col-4 aac-lg-col-3"&gt;Sidebar&lt;/div&gt;
&lt;/div&gt;</code></pre>
            </div>
        </section>

        <!-- Buttons -->
        <section class="demo-section">
            <h2 class="aac-h2">Button Components</h2>
            <div class="aac-row">
                <div class="aac-col-12">
                    <h3 class="aac-h3">Button Variants</h3>
                    <div class="aac-mb-4">
                        <button class="aac-btn aac-btn-primary aac-m-2">Primary</button>
                        <button class="aac-btn aac-btn-secondary aac-m-2">Secondary</button>
                        <button class="aac-btn aac-btn-danger aac-m-2">Danger</button>
                        <button class="aac-btn aac-btn-outline-primary aac-m-2">Outline Primary</button>
                    </div>
                    
                    <h3 class="aac-h3">Button Sizes</h3>
                    <div class="aac-mb-4">
                        <button class="aac-btn aac-btn-primary aac-btn-sm aac-m-2">Small</button>
                        <button class="aac-btn aac-btn-primary aac-m-2">Default</button>
                        <button class="aac-btn aac-btn-primary aac-btn-lg aac-m-2">Large</button>
                    </div>
                </div>
            </div>
            
            <div class="code-block">
                <button class="copy-btn" onclick="copyCode(this, 'buttons')">Copy</button>
                <pre><code>&lt;button class="aac-btn aac-btn-primary"&gt;Primary Button&lt;/button&gt;
&lt;button class="aac-btn aac-btn-outline-primary"&gt;Outline Button&lt;/button&gt;
&lt;button class="aac-btn aac-btn-primary aac-btn-lg"&gt;Large Button&lt;/button&gt;</code></pre>
            </div>
        </section>

        <!-- Modal Components -->
        <section class="demo-section">
            <h2 class="aac-h2">Modal Components</h2>
            <p class="aac-mb-4">Interactive modal dialogs with smooth animations and dark mode support.</p>
            
            <div class="aac-row">
                <div class="aac-col-12 aac-md-col-6">
                    <h3 class="aac-h3">Modal Examples</h3>
                    <div class="aac-mb-4">
                        <button class="aac-btn aac-btn-primary aac-m-2" onclick="openModal('basicModal')">Basic Modal</button>
                        <button class="aac-btn aac-btn-secondary aac-m-2" onclick="openModal('largeModal')">Large Modal</button>
                        <button class="aac-btn aac-btn-outline-primary aac-m-2" onclick="openModal('confirmModal')">Confirm Dialog</button>
                    </div>
                </div>
                <div class="aac-col-12 aac-md-col-6">
                    <div class="code-block">
                        <button class="copy-btn" onclick="copyCode(this, 'modal')">Copy</button>
                        <pre><code>&lt;div class="aac-modal" id="myModal"&gt;
  &lt;div class="aac-modal-content"&gt;
    &lt;div class="aac-modal-header"&gt;
      &lt;h3 class="aac-modal-title"&gt;Modal Title&lt;/h3&gt;
      &lt;button class="aac-modal-close"&gt;&amp;times;&lt;/button&gt;
    &lt;/div&gt;
    &lt;div class="aac-modal-body"&gt;
      &lt;p&gt;Modal content goes here.&lt;/p&gt;
    &lt;/div&gt;
    &lt;div class="aac-modal-footer"&gt;
      &lt;button class="aac-btn aac-btn-primary"&gt;Save&lt;/button&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/div&gt;</code></pre>
                    </div>
                </div>
            </div>
        </section>

        <!-- Slide Menu Components -->
        <section class="demo-section">
            <h2 class="aac-h2">Slide Menu Components</h2>
            <p class="aac-mb-4">Sliding side navigation menus with overlay and smooth animations.</p>
            
            <div class="aac-row">
                <div class="aac-col-12 aac-md-col-6">
                    <h3 class="aac-h3">Slide Menu Examples</h3>
                    <div class="aac-mb-4">
                        <button class="aac-btn aac-btn-primary aac-m-2" onclick="openSlideMenu('leftMenu')">Left Menu</button>
                        <button class="aac-btn aac-btn-secondary aac-m-2" onclick="openSlideMenu('rightMenu')">Right Menu</button>
                        <button class="aac-btn aac-btn-outline-primary aac-m-2" onclick="openSlideMenu('smallMenu')">Small Menu</button>
                    </div>
                </div>
                <div class="aac-col-12 aac-md-col-6">
                    <div class="code-block">
                        <button class="copy-btn" onclick="copyCode(this, 'slidemenu')">Copy</button>
                        <pre><code>&lt;div class="aac-slide-menu" id="myMenu"&gt;
  &lt;div class="aac-slide-menu-header"&gt;
    &lt;h3 class="aac-slide-menu-title"&gt;Navigation&lt;/h3&gt;
    &lt;button class="aac-slide-menu-close"&gt;&amp;times;&lt;/button&gt;
  &lt;/div&gt;
  &lt;div class="aac-slide-menu-body"&gt;
    &lt;a href="#" class="aac-slide-menu-item"&gt;Home&lt;/a&gt;
    &lt;a href="#" class="aac-slide-menu-item"&gt;About&lt;/a&gt;
  &lt;/div&gt;
&lt;/div&gt;</code></pre>
                    </div>
                </div>
            </div>
        </section>

        <!-- Cards -->
        <section class="demo-section">
            <h2 class="aac-h2">Card Components</h2>
            <div class="aac-row">
                <div class="aac-col-12 aac-md-col-4">
                    <div class="aac-card aac-mb-4">
                        <div class="aac-card-header">
                            <h4 class="aac-h4 aac-mb-0">Basic Card</h4>
                        </div>
                        <div class="aac-card-body">
                            <p>This is a basic card component with header, body, and footer sections.</p>
                        </div>
                        <div class="aac-card-footer">
                            <button class="aac-btn aac-btn-primary aac-btn-sm">Action</button>
                        </div>
                    </div>
                </div>
                
                <div class="aac-col-12 aac-md-col-4">
                    <div class="aac-card aac-mb-4">
                        <div class="aac-card-body">
                            <h4 class="aac-h4">Simple Card</h4>
                            <p>A card with just a body section for simpler layouts.</p>
                            <button class="aac-btn aac-btn-outline-primary">Learn More</button>
                        </div>
                    </div>
                </div>
                
                <div class="aac-col-12 aac-md-col-4">
                    <div class="aac-card aac-mb-4">
                        <div class="aac-card-header">
                            <h4 class="aac-h4 aac-mb-0">Feature Card</h4>
                        </div>
                        <div class="aac-card-body">
                            <p class="aac-text-secondary">Perfect for showcasing features or content blocks.</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="code-block">
                <button class="copy-btn" onclick="copyCode(this, 'cards')">Copy</button>
                <pre><code>&lt;div class="aac-card"&gt;
  &lt;div class="aac-card-header"&gt;
    &lt;h4 class="aac-h4 aac-mb-0"&gt;Card Title&lt;/h4&gt;
  &lt;/div&gt;
  &lt;div class="aac-card-body"&gt;
    &lt;p&gt;Card content goes here.&lt;/p&gt;
  &lt;/div&gt;
&lt;/div&gt;</code></pre>
            </div>
        </section>

        <!-- Alerts -->
        <section class="demo-section">
            <h2 class="aac-h2">Alert Components</h2>
            <div class="aac-alert aac-alert-primary">
                <strong>Info!</strong> This is a primary alert message.
            </div>
            <div class="aac-alert aac-alert-success">
                <strong>Success!</strong> Your action was completed successfully.
            </div>
            <div class="aac-alert aac-alert-warning">
                <strong>Warning!</strong> Please check your input data.
            </div>
            <div class="aac-alert aac-alert-danger">
                <strong>Error!</strong> Something went wrong. Please try again.
            </div>
            
            <div class="code-block">
                <button class="copy-btn" onclick="copyCode(this, 'alerts')">Copy</button>
                <pre><code>&lt;div class="aac-alert aac-alert-primary"&gt;Primary alert&lt;/div&gt;
&lt;div class="aac-alert aac-alert-success"&gt;Success alert&lt;/div&gt;
&lt;div class="aac-alert aac-alert-warning"&gt;Warning alert&lt;/div&gt;
&lt;div class="aac-alert aac-alert-danger"&gt;Danger alert&lt;/div&gt;</code></pre>
            </div>
        </section>

        <!-- Forms -->
        <section class="demo-section">
            <h2 class="aac-h2">Form Components</h2>
            <div class="aac-row">
                <div class="aac-col-12 aac-md-col-6">
                    <form>
                        <div class="aac-form-group">
                            <label class="aac-form-label" for="email">Email Address</label>
                            <input type="email" class="aac-form-control" id="email" placeholder="Enter your email">
                        </div>
                        
                        <div class="aac-form-group">
                            <label class="aac-form-label" for="password">Password</label>
                            <input type="password" class="aac-form-control" id="password" placeholder="Enter your password">
                        </div>
                        
                        <div class="aac-form-group">
                            <label class="aac-form-label" for="message">Message</label>
                            <textarea class="aac-form-control" id="message" rows="4" placeholder="Enter your message"></textarea>
                        </div>
                        
                        <button type="submit" class="aac-btn aac-btn-primary">Submit Form</button>
                    </form>
                </div>
                <div class="aac-col-12 aac-md-col-6">
                    <div class="code-block">
                        <button class="copy-btn" onclick="copyCode(this, 'forms')">Copy</button>
                        <pre><code>&lt;div class="aac-form-group"&gt;
  &lt;label class="aac-form-label"&gt;Email&lt;/label&gt;
  &lt;input type="email" class="aac-form-control"&gt;
&lt;/div&gt;

&lt;div class="aac-form-group"&gt;
  &lt;label class="aac-form-label"&gt;Message&lt;/label&gt;
  &lt;textarea class="aac-form-control"&gt;&lt;/textarea&gt;
&lt;/div&gt;</code></pre>
                    </div>
                </div>
            </div>
        </section>

        <!-- Responsive Images -->
        <section class="demo-section">
            <h2 class="aac-h2">Responsive Images</h2>
            <p class="aac-mb-4">Responsive image components that adapt to different screen sizes and styles.</p>
            
            <div class="aac-row">
                <div class="aac-col-12 aac-md-col-6">
                    <h3 class="aac-h3">Image Styles</h3>
                    <div class="aac-mb-4">
                        <img src="https://picsum.photos/300/200" alt="Responsive Image" class="aac-img-responsive aac-mb-3">
                        <img src="https://picsum.photos/300/200?random=1" alt="Thumbnail Image" class="aac-img-thumbnail aac-mb-3">
                        <img src="https://picsum.photos/150/150?random=2" alt="Rounded Image" class="aac-img-rounded aac-mb-3">
                        <img src="https://picsum.photos/150/150?random=3" alt="Circle Image" class="aac-img-circle aac-mb-3">
                    </div>

                    <h3 class="aac-h3">Avatar Sizes</h3>
                    <div class="aac-mb-4">
                        <img src="https://picsum.photos/100/100?random=4" alt="Avatar XS" class="aac-avatar aac-avatar-xs aac-m-1">
                        <img src="https://picsum.photos/100/100?random=5" alt="Avatar SM" class="aac-avatar aac-avatar-sm aac-m-1">
                        <img src="https://picsum.photos/100/100?random=6" alt="Avatar MD" class="aac-avatar aac-avatar-md aac-m-1">
                        <img src="https://picsum.photos/100/100?random=7" alt="Avatar LG" class="aac-avatar aac-avatar-lg aac-m-1">
                        <img src="https://picsum.photos/100/100?random=8" alt="Avatar XL" class="aac-avatar aac-avatar-xl aac-m-1">
                    </div>
                </div>
                <div class="aac-col-12 aac-md-col-6">
                    <div class="code-block">
                        <button class="copy-btn" onclick="copyCode(this, 'images')">Copy</button>
                        <pre><code>&lt;!-- Responsive Images --&gt;
&lt;img src="image.jpg" class="aac-img-responsive" alt="Responsive"&gt;
&lt;img src="image.jpg" class="aac-img-thumbnail" alt="Thumbnail"&gt;
&lt;img src="image.jpg" class="aac-img-rounded" alt="Rounded"&gt;
&lt;img src="image.jpg" class="aac-img-circle" alt="Circle"&gt;

&lt;!-- Avatar Components --&gt;
&lt;img src="avatar.jpg" class="aac-avatar aac-avatar-xs" alt="Avatar"&gt;
&lt;img src="avatar.jpg" class="aac-avatar aac-avatar-sm" alt="Avatar"&gt;
&lt;img src="avatar.jpg" class="aac-avatar aac-avatar-md" alt="Avatar"&gt;
&lt;img src="avatar.jpg" class="aac-avatar aac-avatar-lg" alt="Avatar"&gt;
&lt;img src="avatar.jpg" class="aac-avatar aac-avatar-xl" alt="Avatar"&gt;</code></pre>
                    </div>
                </div>
            </div>
        </section>

        <!-- Responsive Videos -->
        <section class="demo-section">
            <h2 class="aac-h2">Responsive Videos</h2>
            <p class="aac-mb-4">Responsive video containers that maintain aspect ratios across all devices.</p>
            
            <div class="aac-row">
                <div class="aac-col-12 aac-md-col-6">
                    <h3 class="aac-h3">Video Examples</h3>
                    
                    <h4 class="aac-h4">16:9 Aspect Ratio</h4>
                    <div class="aac-video-responsive aac-video-16-9 aac-mb-4">
                        <iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ" title="Demo Video" allowfullscreen></iframe>
                    </div>

                    <h4 class="aac-h4">4:3 Aspect Ratio</h4>
                    <div class="aac-video-responsive aac-video-4-3 aac-mb-4">
                        <iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ" title="Demo Video 4:3" allowfullscreen></iframe>
                    </div>
                </div>
                <div class="aac-col-12 aac-md-col-6">
                    <div class="code-block">
                        <button class="copy-btn" onclick="copyCode(this, 'videos')">Copy</button>
                        <pre><code>&lt;!-- Responsive Video Container --&gt;
&lt;div class="aac-video-responsive"&gt;
  &lt;iframe src="video-url" allowfullscreen&gt;&lt;/iframe&gt;
&lt;/div&gt;

&lt;!-- Different Aspect Ratios --&gt;
&lt;div class="aac-video-responsive aac-video-16-9"&gt;
  &lt;iframe src="video-url"&gt;&lt;/iframe&gt; &lt;!-- 16:9 --&gt;
&lt;/div&gt;

&lt;div class="aac-video-responsive aac-video-4-3"&gt;
  &lt;iframe src="video-url"&gt;&lt;/iframe&gt; &lt;!-- 4:3 --&gt;
&lt;/div&gt;

&lt;div class="aac-video-responsive aac-video-21-9"&gt;
  &lt;iframe src="video-url"&gt;&lt;/iframe&gt; &lt;!-- 21:9 --&gt;
&lt;/div&gt;</code></pre>
                    </div>
                </div>
            </div>
        </section>

        <!-- Image Gallery -->
        <section class="demo-section">
            <h2 class="aac-h2">Image Gallery</h2>
            <p class="aac-mb-4">Responsive image gallery with hover effects and lightbox functionality.</p>
            
            <div class="aac-gallery aac-mb-4">
                <div class="aac-gallery-item" onclick="openLightbox('https://picsum.photos/800/600?random=10')">
                    <img src="https://picsum.photos/300/200?random=10" alt="Gallery Image 1">
                    <div class="aac-gallery-overlay">
                        <span>Click to view</span>
                    </div>
                </div>
                <div class="aac-gallery-item" onclick="openLightbox('https://picsum.photos/800/600?random=11')">
                    <img src="https://picsum.photos/300/200?random=11" alt="Gallery Image 2">
                    <div class="aac-gallery-overlay">
                        <span>Click to view</span>
                    </div>
                </div>
                <div class="aac-gallery-item" onclick="openLightbox('https://picsum.photos/800/600?random=12')">
                    <img src="https://picsum.photos/300/200?random=12" alt="Gallery Image 3">
                    <div class="aac-gallery-overlay">
                        <span>Click to view</span>
                    </div>
                </div>
                <div class="aac-gallery-item" onclick="openLightbox('https://picsum.photos/800/600?random=13')">
                    <img src="https://picsum.photos/300/200?random=13" alt="Gallery Image 4">
                    <div class="aac-gallery-overlay">
                        <span>Click to view</span>
                    </div>
                </div>
            </div>
            
            <div class="code-block">
                <button class="copy-btn" onclick="copyCode(this, 'gallery')">Copy</button>
                <pre><code>&lt;!-- Image Gallery --&gt;
&lt;div class="aac-gallery"&gt;
  &lt;div class="aac-gallery-item"&gt;
    &lt;img src="image1.jpg" alt="Gallery Image"&gt;
    &lt;div class="aac-gallery-overlay"&gt;
      &lt;span&gt;Click to view&lt;/span&gt;
    &lt;/div&gt;
  &lt;/div&gt;
  &lt;!-- Add more gallery items as needed --&gt;
&lt;/div&gt;</code></pre>
            </div>
        </section>

        <!-- Responsive Tables -->
        <section class="demo-section">
            <h2 class="aac-h2">Responsive Tables</h2>
            <p class="aac-mb-4">Responsive table components with various styles and mobile-friendly features.</p>
            
            <h3 class="aac-h3">Basic Table</h3>
            <div class="aac-table-responsive aac-mb-4">
                <table class="aac-table">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Role</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>1</td>
                            <td>John Doe</td>
                            <td><EMAIL></td>
                            <td>Admin</td>
                            <td><span class="aac-badge aac-badge-success">Active</span></td>
                        </tr>
                        <tr>
                            <td>2</td>
                            <td>Jane Smith</td>
                            <td><EMAIL></td>
                            <td>User</td>
                            <td><span class="aac-badge aac-badge-warning">Pending</span></td>
                        </tr>
                        <tr>
                            <td>3</td>
                            <td>Bob Johnson</td>
                            <td><EMAIL></td>
                            <td>Moderator</td>
                            <td><span class="aac-badge aac-badge-primary">Active</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <h3 class="aac-h3">Mobile Stack Table (resize to see)</h3>
            <p class="aac-text-secondary aac-mb-3">This table stacks vertically on mobile devices using data labels.</p>
            <div class="aac-table-responsive aac-mb-4">
                <table class="aac-table aac-table-stack">
                    <thead>
                        <tr>
                            <th>Customer</th>
                            <th>Order Date</th>
                            <th>Status</th>
                            <th>Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td data-label="Customer">Alice Johnson</td>
                            <td data-label="Order Date">2024-01-15</td>
                            <td data-label="Status"><span class="aac-badge aac-badge-success">Completed</span></td>
                            <td data-label="Amount">$156.99</td>
                        </tr>
                        <tr>
                            <td data-label="Customer">Michael Chen</td>
                            <td data-label="Order Date">2024-01-14</td>
                            <td data-label="Status"><span class="aac-badge aac-badge-warning">Processing</span></td>
                            <td data-label="Amount">$89.50</td>
                        </tr>
                        <tr>
                            <td data-label="Customer">Sarah Wilson</td>
                            <td data-label="Order Date">2024-01-13</td>
                            <td data-label="Status"><span class="aac-badge aac-badge-danger">Cancelled</span></td>
                            <td data-label="Amount">$234.75</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <h3 class="aac-h3">Striped & Bordered Table</h3>
            <div class="aac-table-responsive aac-mb-4">
                <table class="aac-table aac-table-striped aac-table-bordered">
                    <thead>
                        <tr>
                            <th>Product</th>
                            <th class="aac-text-center">Price</th>
                            <th class="aac-text-center">Stock</th>
                            <th class="aac-text-right">Total Value</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Laptop Computer</td>
                            <td class="aac-text-center">$999.99</td>
                            <td class="aac-text-center">25</td>
                            <td class="aac-text-right">$24,999.75</td>
                        </tr>
                        <tr>
                            <td>Wireless Mouse</td>
                            <td class="aac-text-center">$29.99</td>
                            <td class="aac-text-center">150</td>
                            <td class="aac-text-right">$4,498.50</td>
                        </tr>
                        <tr>
                            <td>USB Keyboard</td>
                            <td class="aac-text-center">$49.99</td>
                            <td class="aac-text-center">75</td>
                            <td class="aac-text-right">$3,749.25</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="code-block">
                <button class="copy-btn" onclick="copyCode(this, 'tables')">Copy</button>
                <pre><code>&lt;!-- Basic Responsive Table --&gt;
&lt;div class="aac-table-responsive"&gt;
  &lt;table class="aac-table"&gt;
    &lt;thead&gt;
      &lt;tr&gt;
        &lt;th&gt;#&lt;/th&gt;
        &lt;th&gt;Name&lt;/th&gt;
        &lt;th&gt;Email&lt;/th&gt;
      &lt;/tr&gt;
    &lt;/thead&gt;
    &lt;tbody&gt;
      &lt;tr&gt;
        &lt;td&gt;1&lt;/td&gt;
        &lt;td&gt;John Doe&lt;/td&gt;
        &lt;td&gt;<EMAIL>&lt;/td&gt;
      &lt;/tr&gt;
    &lt;/tbody&gt;
  &lt;/table&gt;
&lt;/div&gt;

&lt;!-- Table Variants --&gt;
&lt;table class="aac-table aac-table-striped"&gt;...&lt;/table&gt;
&lt;table class="aac-table aac-table-bordered"&gt;...&lt;/table&gt;

&lt;!-- Mobile Stack Table --&gt;
&lt;table class="aac-table aac-table-stack"&gt;
  &lt;tbody&gt;
    &lt;tr&gt;
      &lt;td data-label="Name"&gt;John Doe&lt;/td&gt;
      &lt;td data-label="Email"&gt;<EMAIL>&lt;/td&gt;
    &lt;/tr&gt;
  &lt;/tbody&gt;
&lt;/table&gt;</code></pre>
            </div>
        </section>

        <!-- Footer -->
        <footer class="aac-text-center aac-p-8 aac-mt-8">
            <p class="aac-text-secondary">CloudFlex CSS Framework v1.0 - Built with modern web standards</p>
            <p class="aac-text-secondary aac-text-sm">Lightweight • Flexible • Dark Mode Ready</p>
        </footer>
    </div>

    <!-- Modal Demonstrations -->
    <div class="aac-modal" id="basicModal">
        <div class="aac-modal-content">
            <div class="aac-modal-header">
                <h3 class="aac-modal-title">Basic Modal</h3>
                <button class="aac-modal-close" onclick="closeModal('basicModal')">&times;</button>
            </div>
            <div class="aac-modal-body">
                <p>This is a basic modal dialog example. You can put any content here including forms, images, or other components.</p>
                <p class="aac-text-secondary">Modal dialogs are great for displaying focused content without navigating away from the current page.</p>
            </div>
            <div class="aac-modal-footer">
                <button class="aac-btn aac-btn-primary" onclick="closeModal('basicModal')">Close</button>
            </div>
        </div>
    </div>

    <div class="aac-modal aac-modal-lg" id="largeModal">
        <div class="aac-modal-content">
            <div class="aac-modal-header">
                <h3 class="aac-modal-title">Large Modal</h3>
                <button class="aac-modal-close" onclick="closeModal('largeModal')">&times;</button>
            </div>
            <div class="aac-modal-body">
                <h4 class="aac-h4">This is a larger modal</h4>
                <p>Large modals are perfect for displaying more detailed content, forms, or complex layouts.</p>
                <div class="aac-row">
                    <div class="aac-col-6">
                        <h5 class="aac-h5">Features</h5>
                        <ul>
                            <li>Responsive design</li>
                            <li>Dark mode support</li>
                            <li>Smooth animations</li>
                            <li>Keyboard navigation</li>
                        </ul>
                    </div>
                    <div class="aac-col-6">
                        <h5 class="aac-h5">Benefits</h5>
                        <ul>
                            <li>Better user experience</li>
                            <li>Improved accessibility</li>
                            <li>Modern appearance</li>
                            <li>Easy to customize</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="aac-modal-footer">
                <button class="aac-btn aac-btn-secondary" onclick="closeModal('largeModal')">Cancel</button>
                <button class="aac-btn aac-btn-primary" onclick="closeModal('largeModal')">Save Changes</button>
            </div>
        </div>
    </div>

    <div class="aac-modal" id="confirmModal">
        <div class="aac-modal-content">
            <div class="aac-modal-header">
                <h3 class="aac-modal-title">Confirm Action</h3>
                <button class="aac-modal-close" onclick="closeModal('confirmModal')">&times;</button>
            </div>
            <div class="aac-modal-body">
                <p>Are you sure you want to proceed with this action? This is typically used for confirmation dialogs.</p>
                <div class="aac-alert aac-alert-warning">
                    <strong>Warning:</strong> This action cannot be undone.
                </div>
            </div>
            <div class="aac-modal-footer">
                <button class="aac-btn aac-btn-secondary" onclick="closeModal('confirmModal')">Cancel</button>
                <button class="aac-btn aac-btn-danger" onclick="closeModal('confirmModal')">Confirm</button>
            </div>
        </div>
    </div>

    <!-- Slide Menu Demonstrations -->
    <div class="aac-slide-menu" id="leftMenu">
        <div class="aac-slide-menu-header">
            <h3 class="aac-slide-menu-title">Navigation Menu</h3>
            <button class="aac-slide-menu-close" onclick="closeSlideMenu('leftMenu')">&times;</button>
        </div>
        <div class="aac-slide-menu-body">
            <a href="#" class="aac-slide-menu-item aac-slide-menu-item-active">Home</a>
            <a href="#" class="aac-slide-menu-item">About</a>
            <a href="#" class="aac-slide-menu-item">Services</a>
            <div class="aac-slide-menu-divider"></div>
            <a href="#" class="aac-slide-menu-item">Portfolio</a>
            <a href="#" class="aac-slide-menu-item">Blog</a>
            <a href="#" class="aac-slide-menu-item">Contact</a>
            <div class="aac-slide-menu-divider"></div>
            <a href="#" class="aac-slide-menu-item">Settings</a>
            <a href="#" class="aac-slide-menu-item">Help</a>
        </div>
    </div>

    <div class="aac-slide-menu aac-slide-menu-right" id="rightMenu">
        <div class="aac-slide-menu-header">
            <h3 class="aac-slide-menu-title">Right Side Menu</h3>
            <button class="aac-slide-menu-close" onclick="closeSlideMenu('rightMenu')">&times;</button>
        </div>
        <div class="aac-slide-menu-body">
            <a href="#" class="aac-slide-menu-item">Dashboard</a>
            <a href="#" class="aac-slide-menu-item">Profile</a>
            <a href="#" class="aac-slide-menu-item">Messages</a>
            <div class="aac-slide-menu-divider"></div>
            <a href="#" class="aac-slide-menu-item">Notifications</a>
            <a href="#" class="aac-slide-menu-item">Privacy</a>
            <a href="#" class="aac-slide-menu-item">Security</a>
            <div class="aac-slide-menu-divider"></div>
            <a href="#" class="aac-slide-menu-item">Logout</a>
        </div>
    </div>

    <div class="aac-slide-menu aac-slide-menu-sm" id="smallMenu">
        <div class="aac-slide-menu-header">
            <h3 class="aac-slide-menu-title">Quick Menu</h3>
            <button class="aac-slide-menu-close" onclick="closeSlideMenu('smallMenu')">&times;</button>
        </div>
        <div class="aac-slide-menu-body">
            <a href="#" class="aac-slide-menu-item">Quick Action 1</a>
            <a href="#" class="aac-slide-menu-item">Quick Action 2</a>
            <a href="#" class="aac-slide-menu-item">Quick Action 3</a>
        </div>
    </div>

    <div class="aac-slide-menu-overlay" id="menuOverlay" onclick="closeAllMenus()"></div>

    <!-- Lightbox for Gallery Images -->
    <div class="aac-lightbox" id="lightbox" onclick="closeLightbox()">
        <div class="aac-lightbox-content" onclick="event.stopPropagation()">
            <button class="aac-lightbox-close" onclick="closeLightbox()">&times;</button>
            <img class="aac-lightbox-img" id="lightboxImg" src="" alt="Gallery Image">
        </div>
    </div>

    <!-- Mobile Navigation -->
    <nav class="aac-mobile-nav" id="mobileNav">
        <div class="aac-mobile-nav-header">
            <a href="#" class="aac-mobile-nav-logo">CloudFlex</a>
            <button class="aac-mobile-nav-close" onclick="closeMobileNav()">&times;</button>
        </div>
        <div class="aac-mobile-nav-search">
            <input type="text" placeholder="Search...">
        </div>
        <div class="aac-mobile-nav-menu">
            <a href="#" class="aac-mobile-nav-item aac-active">Home</a>
            <a href="#" class="aac-mobile-nav-item">Components</a>
            <a href="#" class="aac-mobile-nav-item">Documentation</a>
            <a href="#" class="aac-mobile-nav-item">Examples</a>
            <a href="#" class="aac-mobile-nav-item">About</a>
            <a href="#" class="aac-mobile-nav-item">Contact</a>
        </div>
    </nav>
    <div class="aac-mobile-nav-overlay" id="mobileNavOverlay" onclick="closeMobileNav()"></div>

    <script>
        // Theme management
        let currentTheme = localStorage.getItem('aac-theme') || 'light';
        document.documentElement.setAttribute('data-theme', currentTheme);
        updateThemeIcon();

        function toggleTheme() {
            currentTheme = currentTheme === 'light' ? 'dark' : 'light';
            document.documentElement.setAttribute('data-theme', currentTheme);
            localStorage.setItem('aac-theme', currentTheme);
            updateThemeIcon();
        }

        function updateThemeIcon() {
            const icon = document.getElementById('theme-icon');
            icon.textContent = currentTheme === 'dark' ? '☀️' : '🌙';
        }

        // Modal Functions
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('aac-modal-active');
            }
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('aac-modal-active');
            }
        }

        // Slide Menu Functions
        function openSlideMenu(menuId) {
            const menu = document.getElementById(menuId);
            const overlay = document.getElementById('menuOverlay');
            if (menu && overlay) {
                menu.classList.add('aac-slide-menu-active');
                overlay.classList.add('aac-slide-menu-overlay-active');
            }
        }

        function closeSlideMenu(menuId) {
            const menu = document.getElementById(menuId);
            const overlay = document.getElementById('menuOverlay');
            if (menu && overlay) {
                menu.classList.remove('aac-slide-menu-active');
                overlay.classList.remove('aac-slide-menu-overlay-active');
            }
        }

        function closeAllMenus() {
            closeSlideMenu('leftMenu');
            closeSlideMenu('rightMenu');
            closeSlideMenu('smallMenu');
        }

        // Lightbox Functions
        function openLightbox(imageSrc) {
            const lightbox = document.getElementById('lightbox');
            const lightboxImg = document.getElementById('lightboxImg');
            if (lightbox && lightboxImg) {
                lightboxImg.src = imageSrc;
                lightbox.classList.add('aac-lightbox-active');
            }
        }

        function closeLightbox() {
            const lightbox = document.getElementById('lightbox');
            if (lightbox) {
                lightbox.classList.remove('aac-lightbox-active');
            }
        }

        // Mobile Navigation Functions
        function openMobileNav() {
            const mobileNav = document.getElementById('mobileNav');
            const overlay = document.getElementById('mobileNavOverlay');
            if (mobileNav && overlay) {
                mobileNav.classList.add('aac-active');
                overlay.classList.add('aac-active');
            }
        }

        function closeMobileNav() {
            const mobileNav = document.getElementById('mobileNav');
            const overlay = document.getElementById('mobileNavOverlay');
            if (mobileNav && overlay) {
                mobileNav.classList.remove('aac-active');
                overlay.classList.remove('aac-active');
            }
        }

        // Header User Dropdown
        function toggleUserDropdown() {
            const dropdown = document.getElementById('userDropdown');
            if (dropdown) {
                dropdown.classList.toggle('aac-show');
            }
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('userDropdown');
            const userBtn = event.target.closest('.aac-header-user-btn');
            
            if (dropdown && !userBtn && !dropdown.contains(event.target)) {
                dropdown.classList.remove('aac-show');
            }
        });

        // Copy code functionality
        function copyCode(button, id) {
            const codeBlock = button.nextElementSibling.textContent;
            navigator.clipboard.writeText(codeBlock).then(() => {
                const originalText = button.textContent;
                button.textContent = 'Copied!';
                setTimeout(() => {
                    button.textContent = originalText;
                }, 2000);
            }).catch(err => {
                console.error('Failed to copy: ', err);
            });
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            if (event.target.classList.contains('aac-modal')) {
                event.target.classList.remove('aac-modal-active');
            }
        }

        // Keyboard navigation
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                // Close all modals
                document.querySelectorAll('.aac-modal.aac-modal-active').forEach(modal => {
                    modal.classList.remove('aac-modal-active');
                });
                
                // Close all slide menus
                closeAllMenus();
            }
        });
    </script>
</body>
</html>