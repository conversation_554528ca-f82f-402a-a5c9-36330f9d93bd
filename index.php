<?php get_header(); ?>

<div class="aac-container aac-container-xl">
    <?php get_template_part('template-parts/hero'); ?>
    <!-- Hero Section -->
    <section class="aac-text-center aac-p-8">
        <h1 class="aac-h1"><?php bloginfo('name'); ?>
        </h1>
        <p class="aac-text-lg aac-text-secondary"><?php bloginfo('description'); ?></p>
    </section>

    <!-- Main Content -->
    <div class="aac-row">
        <div class="aac-col-12 aac-md-col-8">
            <?php
			if ( have_posts() ) :

				/* Start the Loop */
                echo '<div class="aac-row">';
				while ( have_posts() ) : the_post();

					/*
					 * Include the Post-Format-specific template for the content.
					 * If you want to override this in a child theme, then include a file
					 * called content-___.php (where ___ is the Post Format name) and that will be used instead.
					 */
                    
					get_template_part( 'template-parts/post/content', get_post_format() );

				endwhile;
                echo '</div>';
				the_posts_pagination(array(
                        'prev_text' => '← Previous',
                        'next_text' => 'Next →',
                        'class' => 'aac-btn aac-btn-outline-primary'
                    ));

			endif;
			?>
        </div>

        <!-- Sidebar -->
        <div class="aac-col-12 aac-md-col-4">
            <?php get_sidebar(); ?>
        </div>
    </div>
</div>

<?php get_footer(); ?>